import os
import warnings
import pandas as pd
import numpy as np
import torch
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, classification_report, confusion_matrix
from sklearn.model_selection import train_test_split
from transformers import AutoTokenizer, AutoModelForSequenceClassification, Trainer, TrainingArguments
from transformers import DataCollatorWithPadding
from torch.utils.data import Dataset
import json

# 设置使用1号GPU
os.environ['CUDA_VISIBLE_DEVICES'] = '1'

warnings.filterwarnings('ignore')

# 设置环境变量
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

# 读取CSV文件
df = pd.read_csv('美中关税战争_筛选结果.csv')

# 标签映射字典
label_mapping = {
    '情感表达': 'ExpressingFeelings',
    '信息分享': 'SharingInformation', 
    '表达主张': 'AssertionPersuasion',
    '认同与联结': 'AffiliatingSupporting',
    '分歧与冲突': 'DissentingConflicting',
    '寻求信息': 'RequestingInformation',
    '号召行动': 'CallingForAction',
    '模糊意图': 'UnclearIntent'
}

# 创建标签到ID的映射
unique_labels = list(label_mapping.values())
label2id = {label: i for i, label in enumerate(unique_labels)}
id2label = {i: label for label, i in label2id.items()}

print(f"标签映射: {label2id}")

# 准备数据
def prepare_data(df):
    texts = []
    labels = []
    
    for index, row in df.iterrows():
        # 组合三个文本字段
        post_content = str(row['原始帖子'])
        comment_on_post = str(row['上下文'])
        comment_on_comment = str(row['用户文本'])
        
        # 将三个文本组合成一个输入
        combined_text = f"Post: {post_content} [SEP] CommentonPost: {comment_on_post} [SEP] CommentonComment: {comment_on_comment}"
        
        # 获取真实标签
        true_label = row['帖文B意图']
        true_label_en = label_mapping.get(true_label, true_label)
        
        texts.append(combined_text)
        labels.append(label2id[true_label_en])
    
    return texts, labels

# 自定义数据集类
class IntentDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_length=512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

# 准备数据
texts, labels = prepare_data(df)
print(f"数据总数: {len(texts)}")
print(f"标签分布: {pd.Series(labels).value_counts()}")

# 划分训练集和测试集
train_texts, test_texts, train_labels, test_labels = train_test_split(
    texts, labels, test_size=0.2, random_state=42, stratify=labels
)

print(f"训练集大小: {len(train_texts)}")
print(f"测试集大小: {len(test_texts)}")

# 初始化tokenizer和模型
model_name = "bert-base-chinese"  # 使用中文BERT
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForSequenceClassification.from_pretrained(
    model_name,
    num_labels=len(unique_labels),
    id2label=id2label,
    label2id=label2id
)

# 创建数据集
train_dataset = IntentDataset(train_texts, train_labels, tokenizer)
test_dataset = IntentDataset(test_texts, test_labels, tokenizer)

# 数据整理器
data_collator = DataCollatorWithPadding(tokenizer=tokenizer)

# 定义评估函数
def compute_metrics(eval_pred):
    predictions, labels = eval_pred
    predictions = np.argmax(predictions, axis=1)
    
    accuracy = accuracy_score(labels, predictions)
    f1_macro = f1_score(labels, predictions, average='macro')
    f1_micro = f1_score(labels, predictions, average='micro')
    f1_weighted = f1_score(labels, predictions, average='weighted')
    precision = precision_score(labels, predictions, average='macro')
    recall = recall_score(labels, predictions, average='macro')
    
    return {
        'accuracy': accuracy,
        'f1_macro': f1_macro,
        'f1_micro': f1_micro,
        'f1_weighted': f1_weighted,
        'precision': precision,
        'recall': recall
    }

# 训练参数
training_args = TrainingArguments(
    output_dir='./bert_intent_classifier',
    num_train_epochs=3,
    per_device_train_batch_size=16,
    per_device_eval_batch_size=16,
    warmup_steps=500,
    weight_decay=0.01,
    logging_dir='./logs',
    logging_steps=10,
    evaluation_strategy="epoch",
    save_strategy="epoch",
    load_best_model_at_end=True,
    metric_for_best_model="f1_macro",
    greater_is_better=True,
    report_to=None  # 禁用wandb等日志记录
)

# 创建训练器
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=test_dataset,
    tokenizer=tokenizer,
    data_collator=data_collator,
    compute_metrics=compute_metrics,
)

# 开始训练
print("开始训练BERT模型...")
trainer.train()

# 在测试集上评估
print("\n=== 在测试集上评估 ===")
eval_results = trainer.evaluate()
print(f"测试集结果: {eval_results}")

# 获取预测结果
predictions = trainer.predict(test_dataset)
predicted_labels = np.argmax(predictions.predictions, axis=1)

# 转换为标签名称
predicted_label_names = [id2label[pred] for pred in predicted_labels]
true_label_names = [id2label[true] for true in test_labels]

# 计算详细指标
print("\n" + "="*80)
print("=== BERT评估结果 ===")
print("="*80)

accuracy = accuracy_score(true_label_names, predicted_label_names)
f1_macro = f1_score(true_label_names, predicted_label_names, average='macro')
f1_micro = f1_score(true_label_names, predicted_label_names, average='micro')
f1_weighted = f1_score(true_label_names, predicted_label_names, average='weighted')
precision_macro = precision_score(true_label_names, predicted_label_names, average='macro')
recall_macro = recall_score(true_label_names, predicted_label_names, average='macro')

print(f"测试集样本数: {len(test_labels)}")
print(f"准确率 (Accuracy): {accuracy:.4f}")
print(f"F1分数 (Macro): {f1_macro:.4f}")
print(f"F1分数 (Micro): {f1_micro:.4f}")
print(f"F1分数 (Weighted): {f1_weighted:.4f}")
print(f"精确率 (Precision Macro): {precision_macro:.4f}")
print(f"召回率 (Recall Macro): {recall_macro:.4f}")

print("\n=== 详细分类报告 ===")
print(classification_report(true_label_names, predicted_label_names))

print("\n=== 混淆矩阵 ===")
cm = confusion_matrix(true_label_names, predicted_label_names)
unique_test_labels = sorted(list(set(true_label_names + predicted_label_names)))
print("标签顺序:", unique_test_labels)
print(cm)

# 保存结果
results_df = pd.DataFrame({
    'index': range(len(true_label_names)),
    'ground_truth': true_label_names,
    'predictions': predicted_label_names,
    'correct': [gt == pred for gt, pred in zip(true_label_names, predicted_label_names)]
})
results_df.to_csv('bert_evaluation_results.csv', index=False, encoding='utf-8')
print(f"\n详细结果已保存到 bert_evaluation_results.csv")

# 计算每个类别的准确率
print("\n=== 各类别准确率 ===")
for label in unique_test_labels:
    label_mask = np.array(true_label_names) == label
    if label_mask.sum() > 0:
        label_accuracy = accuracy_score(
            np.array(true_label_names)[label_mask], 
            np.array(predicted_label_names)[label_mask]
        )
        print(f"{label}: {label_accuracy:.4f} ({label_mask.sum()} 样本)")

# 保存模型
model.save_pretrained('./bert_intent_model')
tokenizer.save_pretrained('./bert_intent_model')
print("\n模型已保存到 ./bert_intent_model")