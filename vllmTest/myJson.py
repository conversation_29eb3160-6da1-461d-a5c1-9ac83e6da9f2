import os
import json
import warnings
import pandas as pd
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, classification_report, confusion_matrix
import numpy as np

warnings.filterwarnings('ignore')

# 设置环境变量
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

from vllm import LLM, SamplingParams
from vllm.sampling_params import GuidedDecodingParams

# 读取CSV文件
df = pd.read_csv('美中关税战争_筛选结果.csv')

# 初始化LLM
llm = LLM(model="Qwen/Qwen2.5-7B-Instruct")

# 存储预测结果和真实标签
predictions = []
ground_truth = []

# 标签映射字典（将中文标签映射为英文）
label_mapping = {
    '情感表达': 'ExpressingFeelings',
    '信息分享': 'SharingInformation', 
    '表达主张': 'AssertionPersuasion',
    '认同与联结': 'AffiliatingSupporting',
    '分歧与冲突': 'DissentingConflicting',
    '寻求信息': 'RequestingInformation',
    '号召行动': 'CallingForAction',
    '模糊意图': 'UnclearIntent'
}

# 第一步：内容规范化的JSON schema
normalization_schema = {
    "type": "object",
    "properties": {
        "Post": {
            "type": "array",
            "items": {"type": "string"},
            "description": "The revised Post content"
        },
        "CommentonPost": {
            "type": "array",
            "items": {"type": "string"},
            "description": "The revised CommentonPost content"
        },
        "CommentonComment": {
            "type": "array",
            "items": {"type": "string"},
            "description": "The revised CommentonComment content"
        }
    },
    "required": ["Post", "CommentonPost", "CommentonComment"],
    "additionalProperties": False
}

# 第二步：主客观分析的JSON schema
analysis_schema = {
    "type": "object",
    "properties": {
        "objective": {
            "type": "object",
            "properties": {
                "Post": {"type": "array", "items": {"type": "string"}},
                "CommentonPost": {"type": "array", "items": {"type": "string"}},
                "CommentonComment": {"type": "array", "items": {"type": "string"}}
            },
            "required": ["Post", "CommentonPost", "CommentonComment"]
        },
        "subjective": {
            "type": "object",
            "properties": {
                "Post": {"type": "array", "items": {"type": "string"}},
                "CommentonPost": {"type": "array", "items": {"type": "string"}},
                "CommentonComment": {"type": "array", "items": {"type": "string"}}
            },
            "required": ["Post", "CommentonPost", "CommentonComment"]
        }
    },
    "required": ["objective", "subjective"],
    "additionalProperties": False
}

# 第三步：情感表达分析的JSON schema
emotion_schema = {
    "type": "object",
    "properties": {
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        },
        "ExpressingFeelings": {
            "type": "boolean",
            "description": "Whether the content expresses emotional feelings"
        }
    },
    "required": ["Analysis","ExpressingFeelings"],
    "additionalProperties": False
}

# 第四步：信息分享分析
sharing_schema = {
    "type": "object",
    "properties": {
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        },
        "SharingInformation": {
            "type": "boolean",
            "description": "Whether the content shares factual information or knowledge"
        }  
    },
    "required": ["Analysis", "SharingInformation"],
    "additionalProperties": False
}
# 第五步：表达主张分析
assertion_schema = {
    "type": "object",
    "properties": {
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        },
        "AssertionPersuasion": {
            "type": "boolean",
            "description": "Whether the content expresses viewpoints or clear stance showing speaker's judgment"
        }
    },
    "required": ["Analysis", "AssertionPersuasion"],
    "additionalProperties": False
}

affiliating_schema = {
    "type": "object",
    "properties": {
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        },
        "AffiliatingSupporting": {
            "type": "boolean",
            "description": "Whether the content expresses positive attitude towards others' viewpoints or behaviors"
        }
    },
    "required": ["Analysis", "AffiliatingSupporting"],
    "additionalProperties": False
}

dissenting_schema = {
    "type": "object",
    "properties": {
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        },
        "DissentingConflicting": {
            "type": "boolean",
            "description": "Whether the content expresses negative attitude, criticism, or sarcasm towards others"
        }
    },
    "required": ["Analysis", "DissentingConflicting"],
    "additionalProperties": False
}

requesting_schema = {
    "type": "object",
    "properties": {
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        },
        "RequestingInformation": {
            "type": "boolean",
            "description": "Whether the content seeks information or others' opinions through questions or requests"
        }
    },
    "required": ["Analysis", "RequestingInformation"],
    "additionalProperties": False
}

calling_schema = {
    "type": "object",
    "properties": {
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        },
        "CallingForAction": {
            "type": "boolean",
            "description": "Whether the content contains instructions or initiatives to prompt others to take action"
        }
    },
    "required": ["Analysis", "CallingForAction"],
    "additionalProperties": False
}


# 最终综合判断的JSON schema
final_judgment_schema = {
    "type": "object",
    "properties": {
        "Reasoning": {
            "type": "string",
            "description": "Detailed reasoning for the final judgment based on all seven analyses"
        },
        "FinalIntent": {
            "type": "string",
            "enum": ["ExpressingFeelings", "SharingInformation", "AssertionPersuasion", 
                    "AffiliatingSupporting", "DissentingConflicting", "RequestingInformation", 
                    "CallingForAction","UnclearIntent"],
            "description": "The final determined intent"
        }
    },
    "required": ["Reasoning","FinalIntent"],
    "additionalProperties": False
}

# 定义要注入的内容变量
# post_content = "#CWSJ头条 美中同意大幅降低关税：美国将对华加征的关税削减至30%，中国则将对美关税下调至10%，同时双方计划展开进一步的贸易谈判。"
# comment_on_post = "是的，特朗普对中国新征收的关税是在现有20%关税基础上增加的，最新的增加使总体关税率提高到104%。这包括对中国商品征收的50%关税和34%的对等关税。"
# comment_on_comment = "104%的数字具有误导性。该政策起始于34%的关税，如果中国报复，则会上升到84%。现有的20%关税不属于这一新政策。贸易政策的清晰性对“美国优先”至关重要。#事实重要。"
# 处理每一行数据
for index, row in df.iterrows():
    print(f"\n=== 处理第 {index + 1} 行数据 ===")
    
    # 从CSV中读取数据
    post_content = row['原始帖子']
    comment_on_post = row['上下文']
    comment_on_comment = row['用户文本']
    true_label = row['帖文B意图']  # 真实标签
    
    print(f"Post: {post_content}")
    print(f"Comment on Post: {comment_on_post}")
    print(f"Comment on Comment: {comment_on_comment}")
    print(f"True Label: {true_label}")

    # 存储真实标签（转换为英文）
    true_label_en = label_mapping.get(true_label, true_label)
    ground_truth.append(true_label_en)

    # 第一步：内容规范化
    normalization_params = SamplingParams(
        guided_decoding=GuidedDecodingParams(json=normalization_schema),
        temperature=0,
        top_p=1,
        max_tokens=1024
    )

    normalization_prompt = f"""Please analyze and complete the missing grammatical parts such as subject, verb, object of the following content, and output in the specified JSON format，Please think step by step:
    - Post: {post_content}
    - CommentonPost: {comment_on_post}
    - CommentonComment: {comment_on_comment}

    Return the result in this exact JSON format:
    {{
        "Post": ["revised post content"],
        "CommentonPost": ["revised comment on post"],
        "CommentonComment": ["revised comment on comment"]
    }}"""

    print("=== The 1st step：Normalization of the Content ===")
    outputs = llm.generate(
        prompts=normalization_prompt,
        sampling_params=normalization_params,
    )

    normalized_text = outputs[0].outputs[0].text
    print("Normalized JSON:")
    print(normalized_text)

    try:
        normalized_data = json.loads(normalized_text)
        print("\nParsed normalized JSON:")
        print(json.dumps(normalized_data, indent=2, ensure_ascii=False))
        
        # 第二步：主客观分析
        analysis_params = SamplingParams(
            guided_decoding=GuidedDecodingParams(json=analysis_schema),
            temperature=0,
            top_p=1,
            max_tokens=1024
        )
        
        analysis_prompt = f"""请分析以下内容，将其分为客观事实和主观观点两部分：

    输入内容：
    {json.dumps(normalized_data, ensure_ascii=False, indent=2)}

    Please divide the content of Post, CommentonPost and CommentonComment into subjective and objective parts,Please think step by step.

    输出格式：
    {{
    "objective": {{
        "Post": ["客观事实1", "客观事实2"],
        "CommentonPost": ["客观事实1", "客观事实2"],
        "CommentonComment": ["客观事实1"]
    }},
    "subjective": {{
        "Post": ["主观观点1", "主观观点2"],
        "CommentonPost": ["主观观点1", "主观观点2"],
        "CommentonComment": ["主观观点1"]
    }}
    }}"""

        print("\n=== 第二步：主客观分析 ===")
        analysis_outputs = llm.generate(
            prompts=analysis_prompt,
            sampling_params=analysis_params,
        )
        
        analysis_text = analysis_outputs[0].outputs[0].text
        print("Analysis JSON:")
        print(analysis_text)
        
        try:
            analysis_data = json.loads(analysis_text)
            print("\nFinal Analysis Result:")
            print(json.dumps(analysis_data, indent=2, ensure_ascii=False))

            # 第三步：情感表达分析  emotion_schema
            emotion_params = SamplingParams(
                guided_decoding=GuidedDecodingParams(json=emotion_schema),
                temperature=0,
                top_p=1,
                max_tokens=1024
            )

            emotion_prompt = f"""基于以下主客观分离后的内容，并结合给出的定义，请一步一步思考，分析CommentonComment是否符合"情感表达"(Expressing Feelings)意图。
            情感表达定义：**直接**通过语言表露说话者的情感状态。
            输入内容：
            {json.dumps(analysis_data, ensure_ascii=False, indent=2)}

        请分析CommentonComment中的主观部分是如何体现或没有体现情感表达意图的，以及客观部分如何支撑这一判断。

            输出格式：
            {{
                "Analysis": "详细分析CommentonComment中主观部分如何体现/没有体现情感表达，以及客观语料如何支撑这一分析的一段话",  
                "ExpressingFeelings": true/false,
            }}
            """

            print("\n=== 第三步：情感表达分析 ===")
            emotion_outputs = llm.generate(
                prompts=emotion_prompt,
                sampling_params=emotion_params,
            )
            
            emotion_text = emotion_outputs[0].outputs[0].text
            print("Emotion Analysis JSON:")
            print(emotion_text)
            
            try:
                emotion_data = json.loads(emotion_text)
                print("\nFinal Emotion Analysis Result:")
                print(json.dumps(emotion_data, indent=2, ensure_ascii=False))

                

                sharing_params = SamplingParams(
                    guided_decoding=GuidedDecodingParams(json=sharing_schema),
                    temperature=0,
                    top_p=1,
                    max_tokens=1024
                )

                sharing_prompt = f"""基于以下主客观分离后的内容，并结合给出的定义，请一步一步思考，分析CommentonComment是否符合"信息分享"(Sharing Information)意图。
                信息分享定义：提供事实或知识传递客观信息。
                输入内容：
                {json.dumps(analysis_data, ensure_ascii=False, indent=2)}

            请分析CommentonComment中的主观部分是如何体现或没有体现信息分享意图的，以及客观部分如何支撑这一判断。
            输出格式：
            {{
                "Analysis": "具体分析CommentonComment中主观部分如何体现/没有体现信息分享，以及客观语料如何支撑这一分析的一段话"
                "SharingInformation": true/false,
            }}
                """

                print("\n=== 第四步：信息分享分析 ===")
                sharing_outputs = llm.generate(
                    prompts=sharing_prompt,
                    sampling_params=sharing_params,
                )

                sharing_text = sharing_outputs[0].outputs[0].text
                print("Sharing Information Analysis JSON:")
                print(sharing_text)

                try:
                    sharing_data = json.loads(sharing_text)
                    print("\nFinal Sharing Information Analysis Result:")
                    print(json.dumps(sharing_data, indent=2, ensure_ascii=False))
                except json.JSONDecodeError as e:
                    print(f"\nSharing Information JSON parsing error: {e}")

                # 第五步：表达主张分析

                assertion_params = SamplingParams(
                    guided_decoding=GuidedDecodingParams(json=assertion_schema),
                    temperature=0,
                    top_p=1,
                    max_tokens=1024
                )

                assertion_prompt = f"""基于以下主客观分离后的内容，并结合给出的定义，请一步一步思考，分析CommentonComment是否符合"表达主张"(Assertion Persuasion)意图。
                表达主张定义：表达观点或明确立场，展示说话者的判断。
                输入内容：
                {json.dumps(analysis_data, ensure_ascii=False, indent=2)}

            请分析CommentonComment中的主观部分是如何体现或没有体现表达主张意图的。

                输出格式：
                {{
                    "Analysis": "详细分析CommentonComment中主观部分如何体现/没有体现表达主张意图"
                    "AssertionPersuasion": true/false,
                }}
                """

                print("\n=== 第五步：表达主张分析 ===")
                assertion_outputs = llm.generate(
                    prompts=assertion_prompt,
                    sampling_params=assertion_params,
                )

                assertion_text = assertion_outputs[0].outputs[0].text
                print("Assertion Persuasion Analysis JSON:")
                print(assertion_text)

                try:
                    assertion_data = json.loads(assertion_text)
                    print("\nFinal Assertion Persuasion Analysis Result:")
                    print(json.dumps(assertion_data, indent=2, ensure_ascii=False))

                    # 第六步：认同与联结分析
                    

                    affiliating_params = SamplingParams(
                        guided_decoding=GuidedDecodingParams(json=affiliating_schema),
                        temperature=0,
                        top_p=1,
                        max_tokens=1024
                    )

                    affiliating_prompt = f"""基于以下主客观分离后的内容，并结合给出的定义，请一步一步思考，分析CommentonComment是否符合"认同与联结"(Affiliating and Supporting)意图。
                    认同与联结定义：表达对他人观点或行为的积极态度（认同、支持、感谢）。
                    输入内容：
                    {json.dumps(analysis_data, ensure_ascii=False, indent=2)}

                请分析CommentonComment中的主观部分是如何体现或没有体现认同与联结意图的，以及客观部分如何支撑这一判断。
                输出格式：
                {{
                    "Analysis": "详细分析CommentonComment中主观部分如何体现/没有体现认同与联结，以及客观语料如何支撑这一分析的一段话"
                    "AffiliatingSupporting": true/false,
                }}
                    """

                    print("\n=== 第六步：认同与联结分析 ===")
                    affiliating_outputs = llm.generate(
                        prompts=affiliating_prompt,
                        sampling_params=affiliating_params,
                    )

                    affiliating_text = affiliating_outputs[0].outputs[0].text
                    print("Affiliating Supporting Analysis JSON:")
                    print(affiliating_text)

                    try:
                        affiliating_data = json.loads(affiliating_text)
                        print("\nFinal Affiliating Supporting Analysis Result:")
                        print(json.dumps(affiliating_data, indent=2, ensure_ascii=False))
                    except json.JSONDecodeError as e:
                        print(f"\nAffiliating Supporting JSON parsing error: {e}")

                    # 第七步：分歧与冲突分析
                    

                    dissenting_params = SamplingParams(
                        guided_decoding=GuidedDecodingParams(json=dissenting_schema),
                        temperature=0,
                        top_p=1,
                        max_tokens=1024
                    )

                    dissenting_prompt = f"""基于以下主客观分离后的内容，并结合给出的定义，请一步一步思考，分析CommentonComment是否符合"分歧与冲突"(Dissenting and Conflicting)意图。
                    分歧与冲突定义：表达对他人观点或行为的否定、批评、讽刺。
                    输入内容：
                    {json.dumps(analysis_data, ensure_ascii=False, indent=2)}

                请分析CommentonComment中的主观部分是如何体现或没有体现分歧与冲突意图的，以及客观部分如何支撑这一判断。

                    输出格式：
                {{
                    "Analysis": "详细分析CommentonComment中主观部分如何体现/没有体现分歧与冲突，以及客观语料如何支撑这一分析的一段话"
                    "DissentingConflicting": true/false,
                }}
                    """

                    print("\n=== 第七步：分歧与冲突分析 ===")
                    dissenting_outputs = llm.generate(
                        prompts=dissenting_prompt,
                        sampling_params=dissenting_params,
                    )

                    dissenting_text = dissenting_outputs[0].outputs[0].text
                    print("Dissenting Conflicting Analysis JSON:")
                    print(dissenting_text)

                    try:
                        dissenting_data = json.loads(dissenting_text)
                        print("\nFinal Dissenting Conflicting Analysis Result:")
                        print(json.dumps(dissenting_data, indent=2, ensure_ascii=False))

                        # 第八步：寻求信息分析

                        requesting_params = SamplingParams(
                            guided_decoding=GuidedDecodingParams(json=requesting_schema),
                            temperature=0,
                            top_p=1,
                            max_tokens=1024
                        )

                        requesting_prompt = f"""基于以下主客观分离后的内容，并结合给出的定义，请一步一步思考，分析CommentonComment是否符合"寻求信息"(Requesting Information)意图。
                        寻求信息定义：通过提问或请求获取信息或他人观点。
                        输入内容：
                        {json.dumps(analysis_data, ensure_ascii=False, indent=2)}

                    请分析CommentonComment中的主观部分是如何体现或没有体现寻求信息意图的，以及客观部分如何支撑这一判断。
                    输出格式：
                {{
                    "Analysis": "详细分析CommentonComment中主观部分如何体现/没有体现寻求信息，以及客观语料如何支撑这一分析的一段话"
                    "RequestingInformation": true/false,
                }}
                        """

                        print("\n=== 第八步：寻求信息分析 ===")
                        requesting_outputs = llm.generate(
                            prompts=requesting_prompt,
                            sampling_params=requesting_params,
                        )

                        requesting_text = requesting_outputs[0].outputs[0].text
                        print("Requesting Information Analysis JSON:")
                        print(requesting_text)

                        try:
                            requesting_data = json.loads(requesting_text)
                            print("\nFinal Requesting Information Analysis Result:")
                            print(json.dumps(requesting_data, indent=2, ensure_ascii=False))
                        except json.JSONDecodeError as e:
                            print(f"\nRequesting Information JSON parsing error: {e}")

                        # 第九步：号召行动分析

                        calling_params = SamplingParams(
                            guided_decoding=GuidedDecodingParams(json=calling_schema),
                            temperature=0,
                            top_p=1,
                            max_tokens=1024
                        )

                        calling_prompt = f"""基于以下主客观分离后的内容，并结合给出的定义，请一步一步思考，分析CommentonComment是否符合"号召行动"(Calling for Action)意图。
                        号召行动定义：指令或倡议促使他人采取行动。
                        输入内容：
                        {json.dumps(analysis_data, ensure_ascii=False, indent=2)}

                    请分析CommentonComment中的主观部分是如何体现或没有体现号召行动意图的，以及客观部分如何支撑这一判断。
                    输出格式：
                {{
                    "Analysis": "详细分析CommentonComment中主观部分如何体现/没有体现号召行动，以及客观语料如何支撑这一分析的一段话"
                    "CallingForAction": true/false,
                }}
                        """

                        print("\n=== 第九步：号召行动分析 ===")
                        calling_outputs = llm.generate(
                            prompts=calling_prompt,
                            sampling_params=calling_params,
                        )

                        calling_text = calling_outputs[0].outputs[0].text
                        print("Calling for Action Analysis JSON:")
                        print(calling_text)

                        # 在所有分析完成后，添加最终综合判断
                        try:
                            calling_data = json.loads(calling_text)
                            print("\nFinal Calling for Action Analysis Result:")
                            print(json.dumps(calling_data, indent=2, ensure_ascii=False))
                            
                            # 收集所有分析结果
                            all_results = {
                                "ExpressingFeelings": emotion_data,
                                "SharingInformation": sharing_data,
                                "AssertionPersuasion": assertion_data,
                                "AffiliatingSupporting": affiliating_data,
                                "DissentingConflicting": dissenting_data,
                                "RequestingInformation": requesting_data,
                                "CallingForAction": calling_data
                            }
                            
                            # 最终综合判断
                            final_params = SamplingParams(
                                guided_decoding=GuidedDecodingParams(json=final_judgment_schema),
                                temperature=0,
                                top_p=1,
                                max_tokens=8192
                            )
                            
                            final_prompt = f"""基于以下七个意图的定义、分析的结果和理由以及你的判断，最终判断CommentonComment所表达的最终意图最贴近哪个标签，请你一步一步思考。

    分析结果：
    {json.dumps(all_results, ensure_ascii=False, indent=2)}

    请从以下八个意图中选择最符合的一个作为最终判断：
    1. ExpressingFeelings (情感表达)：直接通过语言表露说话者的情感状态。
    2. SharingInformation (信息分享) ：提供事实或知识传递客观信息。
    3. AssertionPersuasion (表达主张)：表达观点或明确立场，展示说话者的判断。
    4. AffiliatingSupporting (认同与联结)：表达对他人观点或行为的积极态度（认同、支持、感谢）。
    5. DissentingConflicting (分歧与冲突)：表达对他人观点或行为的否定、批评、讽刺。
    6. RequestingInformation (寻求信息)：通过提问或请求获取信息或他人观点。
    7. CallingForAction (号召行动)：指令或倡议促使他人采取行动。
    8. UnclearIntent (模糊意图)

    输出格式：
    {{
        "Reasoning": "详细分析CommentonComment为什么体现的是这个意图，以及为什么不是其他意图",
        "FinalIntent": "选择的最终意图"
    }}"""

                            print("\n=== 最终综合判断 ===")
                            final_outputs = llm.generate(
                                prompts=final_prompt,
                                sampling_params=final_params,
                            )
                            
                            final_text = final_outputs[0].outputs[0].text
                            print("Final Judgment JSON:")
                            print(final_text)

                            try:
                                final_data = json.loads(final_text)
                                predicted_label = final_data['FinalIntent']
                                predictions.append(predicted_label)
                                
                                print("\n=== 最终判断结果 ===")
                                print(f"预测意图: {predicted_label}")
                                print(f"真实意图: {true_label_en}")
                                print(f"预测正确: {'✓' if predicted_label == true_label_en else '✗'}")
                                print(f"推理过程: {final_data['Reasoning']}")
                                final_data = json.loads(final_text)
                                print(json.dumps(final_data, indent=2, ensure_ascii=False))
                            except json.JSONDecodeError as e:
                                print(f"\nFinal Judgment JSON parsing error: {e}")
                                predictions.append('UnclearIntent')  # 解析失败时使用默认标签
                                
                        except json.JSONDecodeError as e:
                            print(f"\nCalling for Action JSON parsing error: {e}")

                    except json.JSONDecodeError as e:
                        print(f"\nDissenting Conflicting JSON parsing error: {e}")

                except json.JSONDecodeError as e:
                    print(f"\nAssertion Persuasion JSON parsing error: {e}")

            except json.JSONDecodeError as e:
                print(f"\nEmotion JSON parsing error: {e}")

        except json.JSONDecodeError as e:
            print(f"\nAnalysis JSON parsing error: {e}")

    except json.JSONDecodeError as e:
        print(f"\nNormalization JSON parsing error: {e}")

# 计算评估指标
print("\n" + "="*80)
print("=== 评估结果 ===")
print("="*80)

# 确保预测结果和真实标签数量一致
if len(predictions) != len(ground_truth):
    print(f"警告: 预测结果数量({len(predictions)})与真实标签数量({len(ground_truth)})不一致")
    min_len = min(len(predictions), len(ground_truth))
    predictions = predictions[:min_len]
    ground_truth = ground_truth[:min_len]

# 计算各项指标
accuracy = accuracy_score(ground_truth, predictions)
f1_macro = f1_score(ground_truth, predictions, average='macro')
f1_micro = f1_score(ground_truth, predictions, average='micro')
f1_weighted = f1_score(ground_truth, predictions, average='weighted')
precision_macro = precision_score(ground_truth, predictions, average='macro')
recall_macro = recall_score(ground_truth, predictions, average='macro')

print(f"样本总数: {len(ground_truth)}")
print(f"准确率 (Accuracy): {accuracy:.4f}")
print(f"F1分数 (Macro): {f1_macro:.4f}")
print(f"F1分数 (Micro): {f1_micro:.4f}")
print(f"F1分数 (Weighted): {f1_weighted:.4f}")
print(f"精确率 (Precision Macro): {precision_macro:.4f}")
print(f"召回率 (Recall Macro): {recall_macro:.4f}")

print("\n=== 详细分类报告 ===")
print(classification_report(ground_truth, predictions, target_names=None))

print("\n=== 混淆矩阵 ===")
cm = confusion_matrix(ground_truth, predictions)
unique_labels = sorted(list(set(ground_truth + predictions)))
print("标签顺序:", unique_labels)
print(cm)

# 保存结果到文件
results_df = pd.DataFrame({
    'index': range(len(ground_truth)),
    'ground_truth': ground_truth,
    'predictions': predictions,
    'correct': [gt == pred for gt, pred in zip(ground_truth, predictions)]
})
results_df.to_csv('evaluation_results.csv', index=False, encoding='utf-8')
print(f"\n详细结果已保存到 evaluation_results.csv")

# 计算每个类别的准确率
print("\n=== 各类别准确率 ===")
for label in unique_labels:
    label_mask = np.array(ground_truth) == label
    if label_mask.sum() > 0:
        label_accuracy = accuracy_score(
            np.array(ground_truth)[label_mask], 
            np.array(predictions)[label_mask]
        )
        print(f"{label}: {label_accuracy:.4f} ({label_mask.sum()} 样本)")