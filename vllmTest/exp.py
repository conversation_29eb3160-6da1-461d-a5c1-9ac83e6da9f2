import os
import json
import warnings
import pandas as pd
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, classification_report, confusion_matrix
import numpy as np

# 设置使用1号GPU
os.environ['CUDA_VISIBLE_DEVICES'] = '1'

warnings.filterwarnings('ignore')

# 设置环境变量
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

from vllm import LLM, SamplingParams
from vllm.sampling_params import GuidedDecodingParams

# 读取CSV文件
df = pd.read_csv('美中关税战争_筛选结果.csv')

# 初始化LLM
llm = LLM(model="Qwen/Qwen2.5-7B-Instruct")

# 存储预测结果和真实标签
predictions = []
ground_truth = []

# 标签映射字典（将中文标签映射为英文）
label_mapping = {
    '情感表达': 'ExpressingFeelings',
    '信息分享': 'SharingInformation', 
    '表达主张': 'AssertionPersuasion',
    '认同与联结': 'AffiliatingSupporting',
    '分歧与冲突': 'DissentingConflicting',
    '寻求信息': 'RequestingInformation',
    '号召行动': 'CallingForAction',
    '模糊意图': 'UnclearIntent'
}

# 行为类分析的JSON schema
action_schema = {
    "type": "object",
    "properties": {
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of does CommentonComment meet the definition of SharingInformation，RequestingInformation or CallingForAction"
        },
        "action_intent": {
            "type": "string",
            "enum": ["SharingInformation", "RequestingInformation", "CallingForAction","none"],
            "description": "The intent of the CommentonComment, if the CommentonComment does not meet the definition of SharingInformation，RequestingInformation and CallingForAction, return 'none'"
        }
    },
    "required": ["Analysis","action_intent"],
    "additionalProperties": False
}

# 第三步：情感表达分析的JSON schema
emotion_schema = {
    "type": "object",
    "properties": {
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of does CommentonComment meet the definition of ExpressingFeelings"
        },
        "ExpressingFeelings": {
            "type": "boolean",
            "description": "Whether the CommentonComment meets the definition of ExpressingFeelings"
        }
    },
    "required": ["Analysis","ExpressingFeelings"],
    "additionalProperties": False
}

# 立场类分析的JSON schema
stance_schema = {
    "type": "object",
    "properties": {
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of does CommentonComment meet the definition of AssertionPersuasion,AffiliatingSupporting or DissentingConflicting"
        },
        "stance_intent": {
            "type": "string",
            "enum": ["AssertionPersuasion", "AffiliatingSupporting", "DissentingConflicting","none"],
            "description": "The intent of the CommentonComment, if the CommentonComment does not meet the definition of AssertionPersuasion,AffiliatingSupporting and DissentingConflicting, return 'none'"
        }
    },
    "required": ["Analysis","stance_intent"],
    "additionalProperties": False
}

schema = {
    "type": "object",
    "properties": {
        "Reasoning": {
            "type": "string",
            "description": "Detailed analysis of which intent CommentonComment belongs to among the following intents according to the analysis of Action,Emotion,Stance and the definition of each intent"
        },
        "Intent": {
            "type": "string",
            "enum": ["ExpressingFeelings", "SharingInformation", "AssertionPersuasion", 
                    "AffiliatingSupporting", "DissentingConflicting", "RequestingInformation", 
                    "CallingForAction","UnclearIntent"],
            "description": "The final determined intent"
        }
    },
    "required": ["Reasoning","Intent"],
    "additionalProperties": False
}

# 处理每一行数据
for index, row in df.iterrows():
    print(f"\n=== 处理第 {index + 1} 行数据 ===")
    
    # 从CSV中读取数据
    post_content = row['原始帖子']
    comment_on_post = row['上下文']
    comment_on_comment = row['用户文本']
    true_label = row['帖文B意图']  # 真实标签
    
    print(f"Post: {post_content}")
    print(f"Comment on Post: {comment_on_post}")
    print(f"Comment on Comment: {comment_on_comment}")
    print(f"True Label: {true_label}")
    
    # 存储真实标签（转换为英文）
    true_label_en = label_mapping.get(true_label, true_label)
    ground_truth.append(true_label_en)
    

    action_params = SamplingParams(
        guided_decoding=GuidedDecodingParams(json=action_schema),
        temperature=0,
        top_p=1,
        max_tokens=1024
    )

    action_prompt = f"""You are an expert of intent of action,please give your detailed analysis of does CommentonComment meet the definition of SharingInformation，RequestingInformation or CallingForAction.Please think step by step.
    SharingInformation (信息分享)定义 ：提供事实或知识传递客观信息。
    RequestingInformation (寻求信息)定义 ：通过提问或请求获取信息或他人观点。
    CallingForAction (号召行动)定义 ：指令或倡议促使他人采取行动。
    Post: {post_content}
    Comment on Post: {comment_on_post}
    Comment on Comment: {comment_on_comment}

    Output format:
    {{
        "Analysis": "Detailed analysis of does CommentonComment meet the definition of SharingInformation，RequestingInformation or CallingForAction",
        "action_intent": "SharingInformation" or "RequestingInformation" or "CallingForAction" or "none"
    }}
    """

    action_outputs = llm.generate(
        prompts=action_prompt,
        sampling_params=action_params,
    )

    action_text = action_outputs[0].outputs[0].text
    print("Action Analysis JSON:")
    print(action_text)

    try:
        action_data = json.loads(action_text)
        print("\nAction Analysis Result:")
        print(json.dumps(action_data, indent=2, ensure_ascii=False))
        
        action_params = SamplingParams(
            guided_decoding=GuidedDecodingParams(json=action_schema),
            temperature=0,
            top_p=1,
            max_tokens=1024
        )

        action_prompt = f"""You are an expert of intent of action,please give your detailed analysis of does CommentonComment meet the definition of SharingInformation，RequestingInformation or CallingForAction.Please think step by step.
        SharingInformation (信息分享)定义 ：提供事实或知识传递客观信息。
        RequestingInformation (寻求信息)定义 ：通过提问或请求获取信息或他人观点。
        CallingForAction (号召行动)定义 ：指令或倡议促使他人采取行动。
        Post: {post_content}
        Comment on Post: {comment_on_post}
        Comment on Comment: {comment_on_comment}
        
        Output format:
        {{
            "Analysis": "Detailed analysis of does CommentonComment meet the definition of SharingInformation，RequestingInformation or CallingForAction",
            "action_intent": "SharingInformation" or "RequestingInformation" or "CallingForAction" or "none"
        }}
        """

        action_outputs = llm.generate(
            prompts=action_prompt,
            sampling_params=action_params,
        )

        action_text = action_outputs[0].outputs[0].text
        print("Action Analysis JSON:")
        print(action_text)

        try:
            action_data = json.loads(action_text)
            print("\nAction Analysis Result:")
            print(json.dumps(action_data, indent=2, ensure_ascii=False))

            # 第三步：情感表达分析  emotion_schema
            emotion_params = SamplingParams(
                guided_decoding=GuidedDecodingParams(json=emotion_schema),
                temperature=0,
                top_p=1,
                max_tokens=1024
            )

            emotion_prompt = f"""You are a emotional expert,please give your detailed analysis of does CommentonComment meet the definition of ExpressingFeelings.
            情感表达定义：直接通过语言表露说话者的情感状态。
            输入内容：
            Post: {post_content}
            Comment on Post: {comment_on_post}
            Comment on Comment: {comment_on_comment}

            输出格式：
            {{
                "Analysis": "详细分析CommentonComment中主观部分如何体现/没有体现情感表达，以及客观语料如何支撑这一分析的一段话",  
                "ExpressingFeelings": true/false,
            }}
            """

            print("\n=== 第三步：情感表达分析 ===")
            emotion_outputs = llm.generate(
                prompts=emotion_prompt,
                sampling_params=emotion_params,
            )
            
            emotion_text = emotion_outputs[0].outputs[0].text
            print("Emotion Analysis JSON:")
            print(emotion_text)

            try:
                emotion_data = json.loads(emotion_text)
                print("\nEmotion Analysis Result:")
                print(json.dumps(emotion_data, indent=2, ensure_ascii=False))


                stance_params = SamplingParams(
                    guided_decoding=GuidedDecodingParams(json=stance_schema),
                    temperature=0,
                    top_p=1,
                    max_tokens=1024
                )

                stance_prompt = f"""You are an expert of intent of stance,please give your detailed analysis of does CommentonComment meet the definition of AssertionPersuasion,AffiliatingSupporting or DissentingConflicting.Please think step by step.
                AssertionPersuasion (表达主张)定义 ：表达观点或明确立场，展示说话者的判断。
                AffiliatingSupporting (认同与联结)定义 ：表达对他人观点或立场的认同或支持。
                DissentingConflicting (分歧与冲突)定义 ：表达对他人观点或立场的反对或冲突。
                Post: {post_content}
                Comment on Post: {comment_on_post}
                Comment on Comment: {comment_on_comment}

                Output format:
                {{
                    "Analysis": "Detailed analysis of does CommentonComment meet the definition of AssertionPersuasion,AffiliatingSupporting or DissentingConflicting",
                    "stance_intent": "AssertionPersuasion" or "AffiliatingSupporting" or "DissentingConflicting" or "none"
                }}
                """

                stance_outputs = llm.generate(
                    prompts=stance_prompt,
                    sampling_params=stance_params,
                )

                stance_text = stance_outputs[0].outputs[0].text
                print("Stance Analysis JSON:")
                print(stance_text)

                try:
                    stance_data = json.loads(stance_text)
                    print("\nStance Analysis Result:")
                    print(json.dumps(stance_data, indent=2, ensure_ascii=False))
                    
                    params = SamplingParams(
                        guided_decoding=GuidedDecodingParams(json=schema),
                        temperature=0,
                        top_p=1,
                        max_tokens=1024
                    )

                    prompt = f"""You are an expert of intent,please give your detailed analysis of which intent CommentonComment belongs to among the following intents according to the analysis of Action,Emotion,Stance and the definition of each intent.Please think step by step.
                    1. ExpressingFeelings (情感表达)定义：直接通过语言表露说话者的情感状态。
                    2. SharingInformation (信息分享)定义：提供事实或知识传递客观信息。
                    3. AssertionPersuasion (表达主张)定义：表达观点或明确立场，展示说话者的判断。
                    4. AffiliatingSupporting (认同与联结)定义：表达对他人观点或行为的积极态度（认同、支持、感谢）。
                    5. DissentingConflicting (分歧与冲突)定义：表达对他人观点或行为的否定、批评、讽刺。
                    6. RequestingInformation (寻求信息)定义：通过提问或请求获取信息或他人观点。
                    7. CallingForAction (号召行动)定义：指令或倡议促使他人采取行动。
                    8. UnclearIntent (模糊意图)
                    Post: {post_content}
                    Comment on Post: {comment_on_post}
                    Comment on Comment: {comment_on_comment}
                    Action Analysis: {action_data}
                    Emotion Analysis: {emotion_data}
                    Stance Analysis: {stance_data}
                    Output format:
                    {{
                        "Reasoning": "Detailed reasoning for the judgment",
                        "Intent": "ExpressingFeelings" or "SharingInformation" or "AssertionPersuasion" or "AffiliatingSupporting" or "DissentingConflicting" or "RequestingInformation" or "CallingForAction" or "UnclearIntent"
                    }}
                    """
                    outputs = llm.generate(
                        prompts=prompt,
                        sampling_params=params,
                    )
                    text = outputs[0].outputs[0].text
                    print("Intent Analysis JSON:")
                    print(text)
                    try:
                        data = json.loads(text)
                        predicted_label=data['Intent']
                        
                        print("\nIntent Analysis Result:")
                        predictions.append(predicted_label)
                        print(f"预测意图: {predicted_label}")
                        print(f"真实意图: {true_label_en}")
                        print(f"预测正确: {'✓' if predicted_label == true_label_en else '✗'}")
                        print(f"推理过程: {data['Reasoning']}")
                        print(json.dumps(data, indent=2, ensure_ascii=False))
                    except json.JSONDecodeError as e:
                        print(f"\nIntent JSON parsing error: {e}")
                        predictions.append('UnclearIntent')

                except json.JSONDecodeError as e:
                    print(f"\nStance JSON parsing error: {e}")
            except json.JSONDecodeError as e:
                print(f"\nEmotion JSON parsing error: {e}")

        except json.JSONDecodeError as e:
            print(f"\nAction JSON parsing error: {e}")
    except json.JSONDecodeError as e:
        print(f"\nAction JSON parsing error: {e}")

# 计算评估指标
print("\n" + "="*80)
print("=== 评估结果 ===")
print("="*80)

# 确保预测结果和真实标签数量一致
if len(predictions) != len(ground_truth):
    print(f"警告: 预测结果数量({len(predictions)})与真实标签数量({len(ground_truth)})不一致")
    min_len = min(len(predictions), len(ground_truth))
    predictions = predictions[:min_len]
    ground_truth = ground_truth[:min_len]

# 计算各项指标
accuracy = accuracy_score(ground_truth, predictions)
f1_macro = f1_score(ground_truth, predictions, average='macro', zero_division=0)
f1_micro = f1_score(ground_truth, predictions, average='micro', zero_division=0)
f1_weighted = f1_score(ground_truth, predictions, average='weighted', zero_division=0)
precision_macro = precision_score(ground_truth, predictions, average='macro', zero_division=0)
recall_macro = recall_score(ground_truth, predictions, average='macro', zero_division=0)

print(f"样本总数: {len(ground_truth)}")
print(f"准确率 (Accuracy): {accuracy:.4f}")
print(f"F1分数 (Macro): {f1_macro:.4f}")
print(f"F1分数 (Micro): {f1_micro:.4f}")
print(f"F1分数 (Weighted): {f1_weighted:.4f}")
print(f"精确率 (Precision Macro): {precision_macro:.4f}")
print(f"召回率 (Recall Macro): {recall_macro:.4f}")

print("\n=== 详细分类报告 ===")
print(classification_report(ground_truth, predictions, target_names=None, zero_division=0))

print("\n=== 混淆矩阵 ===")
cm = confusion_matrix(ground_truth, predictions)
unique_labels = sorted(list(set(ground_truth + predictions)))
print("标签顺序:", unique_labels)
print(cm)

# 保存结果到文件
results_df = pd.DataFrame({
    'index': range(len(ground_truth)),
    'ground_truth': ground_truth,
    'predictions': predictions,
    'correct': [gt == pred for gt, pred in zip(ground_truth, predictions)]
})
results_df.to_csv('baseline_evaluation_results.csv', index=False, encoding='utf-8')
print(f"\n详细结果已保存到 baseline_evaluation_results.csv")

# 计算每个类别的准确率
print("\n=== 各类别准确率 ===")
for label in unique_labels:
    label_mask = np.array(ground_truth) == label
    if label_mask.sum() > 0:
        label_accuracy = accuracy_score(
            np.array(ground_truth)[label_mask], 
            np.array(predictions)[label_mask]
        )
        print(f"{label}: {label_accuracy:.4f} ({label_mask.sum()} 样本)")

print("\n=== 预测标签分布 ===")
pred_counts = pd.Series(predictions).value_counts()
print(pred_counts)

print("\n=== 真实标签分布 ===")
true_counts = pd.Series(ground_truth).value_counts()
print(true_counts)