import os
import warnings
import pandas as pd
import numpy as np
import torch
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, classification_report, confusion_matrix
from transformers import AutoTokenizer, AutoModel
import torch.nn.functional as F

# 设置使用1号GPU
os.environ['CUDA_VISIBLE_DEVICES'] = '1'

warnings.filterwarnings('ignore')

# 设置环境变量
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

# 读取CSV文件
df = pd.read_csv('美中关税战争_筛选结果.csv')

# 标签映射字典
label_mapping = {
    '情感表达': 'ExpressingFeelings',
    '信息分享': 'SharingInformation', 
    '表达主张': 'AssertionPersuasion',
    '认同与联结': 'AffiliatingSupporting',
    '分歧与冲突': 'DissentingConflicting',
    '寻求信息': 'RequestingInformation',
    '号召行动': 'CallingForAction',
    '模糊意图': 'UnclearIntent'
}

# 定义标签描述（用于零样本分类）
label_descriptions = {
    'ExpressingFeelings': '直接通过语言表露说话者的情感状态，表达个人感受和情绪',
    'SharingInformation': '提供事实或知识传递客观信息，分享具体的数据或消息',
    'AssertionPersuasion': '表达观点或明确立场，展示说话者的判断和主张',
    'AffiliatingSupporting': '表达对他人观点或行为的积极态度，如认同、支持、感谢',
    'DissentingConflicting': '表达对他人观点或行为的否定、批评、讽刺或反对',
    'RequestingInformation': '通过提问或请求获取信息或他人观点，寻求帮助',
    'CallingForAction': '指令或倡议促使他人采取行动，号召或建议做某事',
    'UnclearIntent': '意图不明确，无法明确归类到其他类别'
}

print("初始化模型...")

# 使用本地BERT模型
try:
    model_name = "bert-base-chinese"
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    bert_model = AutoModel.from_pretrained(model_name)
    
    # 如果有GPU，移动到GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    bert_model = bert_model.to(device)
    bert_model.eval()
    
    print(f"成功加载BERT模型，使用设备: {device}")
    
except Exception as e:
    print(f"BERT模型加载失败: {e}")
    print("请确保已安装transformers库并且网络连接正常")
    exit(1)

def get_text_embedding(text):
    """获取文本的BERT嵌入"""
    inputs = tokenizer(text, return_tensors='pt', truncation=True, padding=True, max_length=512)
    inputs = {k: v.to(device) for k, v in inputs.items()}
    
    with torch.no_grad():
        outputs = bert_model(**inputs)
        # 使用[CLS]标记的嵌入作为句子表示
        embeddings = outputs.last_hidden_state[:, 0, :]
    
    return embeddings.squeeze().cpu()

# 存储预测结果和真实标签
predictions = []
ground_truth = []

print("开始零样本分类...")

# 预先计算标签描述的嵌入
print("计算标签描述嵌入...")
label_embeddings = {}
for label, description in label_descriptions.items():
    label_embeddings[label] = get_text_embedding(description)
    print(f"  {label}: 完成")

# 处理每一行数据
for index, row in df.iterrows():
    print(f"\n=== 处理第 {index + 1} 行数据 ===")
    
    # 从CSV中读取数据
    post_content = str(row['原始帖子'])
    comment_on_post = str(row['上下文'])
    comment_on_comment = str(row['用户文本'])
    true_label = row['帖文B意图']  # 真实标签
    
    print(f"Post: {post_content}")
    print(f"Comment on Post: {comment_on_post}")
    print(f"Comment on Comment: {comment_on_comment}")
    print(f"True Label: {true_label}")
    
    # 存储真实标签（转换为英文）
    true_label_en = label_mapping.get(true_label, true_label)
    ground_truth.append(true_label_en)
    
    # 组合文本
    combined_text = f"Post: {post_content} CommentonPost: {comment_on_post} CommentonComment: {comment_on_comment}"
    
    # 计算文本嵌入
    text_embedding = get_text_embedding(combined_text)
    
    # 计算与每个标签描述的相似度
    similarities = {}
    for label, label_emb in label_embeddings.items():
        similarity = F.cosine_similarity(text_embedding.unsqueeze(0), label_emb.unsqueeze(0))
        similarities[label] = similarity.item()
    
    # 选择相似度最高的标签
    predicted_label = max(similarities, key=similarities.get)
    predictions.append(predicted_label)
    
    print(f"相似度分数:")
    for label, score in sorted(similarities.items(), key=lambda x: x[1], reverse=True):
        print(f"  {label}: {score:.4f}")
    
    print(f"预测意图: {predicted_label}")
    print(f"真实意图: {true_label_en}")
    print(f"预测正确: {'✓' if predicted_label == true_label_en else '✗'}")

# 计算评估指标
print("\n" + "="*80)
print("=== 零样本BERT评估结果 ===")
print("="*80)

# 确保预测结果和真实标签数量一致
if len(predictions) != len(ground_truth):
    print(f"警告: 预测结果数量({len(predictions)})与真实标签数量({len(ground_truth)})不一致")
    min_len = min(len(predictions), len(ground_truth))
    predictions = predictions[:min_len]
    ground_truth = ground_truth[:min_len]

# 计算各项指标
accuracy = accuracy_score(ground_truth, predictions)
f1_macro = f1_score(ground_truth, predictions, average='macro', zero_division=0)
f1_micro = f1_score(ground_truth, predictions, average='micro', zero_division=0)
f1_weighted = f1_score(ground_truth, predictions, average='weighted', zero_division=0)
precision_macro = precision_score(ground_truth, predictions, average='macro', zero_division=0)
recall_macro = recall_score(ground_truth, predictions, average='macro', zero_division=0)

print(f"样本总数: {len(ground_truth)}")
print(f"准确率 (Accuracy): {accuracy:.4f}")
print(f"F1分数 (Macro): {f1_macro:.4f}")
print(f"F1分数 (Micro): {f1_micro:.4f}")
print(f"F1分数 (Weighted): {f1_weighted:.4f}")
print(f"精确率 (Precision Macro): {precision_macro:.4f}")
print(f"召回率 (Recall Macro): {recall_macro:.4f}")

print("\n=== 详细分类报告 ===")
print(classification_report(ground_truth, predictions, target_names=None, zero_division=0))

print("\n=== 混淆矩阵 ===")
cm = confusion_matrix(ground_truth, predictions)
unique_labels = sorted(list(set(ground_truth + predictions)))
print("标签顺序:", unique_labels)
print(cm)

# 保存结果到文件
results_df = pd.DataFrame({
    'index': range(len(ground_truth)),
    'ground_truth': ground_truth,
    'predictions': predictions,
    'correct': [gt == pred for gt, pred in zip(ground_truth, predictions)]
})
results_df.to_csv('bert_zero_shot_results.csv', index=False, encoding='utf-8')
print(f"\n详细结果已保存到 bert_zero_shot_results.csv")

# 计算每个类别的准确率
print("\n=== 各类别准确率 ===")
for label in unique_labels:
    label_mask = np.array(ground_truth) == label
    if label_mask.sum() > 0:
        label_accuracy = accuracy_score(
            np.array(ground_truth)[label_mask], 
            np.array(predictions)[label_mask]
        )
        print(f"{label}: {label_accuracy:.4f} ({label_mask.sum()} 样本)")

print("\n=== 预测标签分布 ===")
pred_counts = pd.Series(predictions).value_counts()
print(pred_counts)

print("\n=== 真实标签分布 ===")
true_counts = pd.Series(ground_truth).value_counts()
print(true_counts)
