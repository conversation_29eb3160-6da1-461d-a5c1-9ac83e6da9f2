import os
import json
import warnings
import pandas as pd
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, classification_report, confusion_matrix
import numpy as np

warnings.filterwarnings('ignore')

# 设置环境变量
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

from vllm import LLM, SamplingParams
from vllm.sampling_params import GuidedDecodingParams

# 读取CSV文件
df = pd.read_csv('美中关税战争_筛选结果.csv')

# 初始化LLM
llm = LLM(model="Qwen/Qwen2.5-7B-Instruct")

# 存储预测结果和真实标签
predictions = []
ground_truth = []

# 标签映射字典（将中文标签映射为英文）
label_mapping = {
    '情感表达': 'Expressing Feelings',
    '信息分享': 'Sharing Information',
    '表达主张': 'Assertion Persuasion',
    '认同与联结': 'Affiliating and Supporting',
    '分歧与冲突': 'Dissenting and Conflicting',
    '寻求信息': 'Requesting Information',
    '号召行动': 'Calling For Action',
    '模糊意图': 'Unclear Intent'
}

# 行为类分析的JSON schema
action_schema = {
    "type": "object",
    "properties": {
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of does CommentonComment meet the definition of Sharing Information，Requesting Information or Calling For Action"
        },
        "action_intent": {
            "type": "string",
            "enum": ["Sharing Information", "Requesting Information", "Calling For Action","none"],
            "description": "The intent of the CommentonComment, if the CommentonComment does not meet the definition of Sharing Information，Requesting Information and Calling For Action, return 'none'"
        }
    },
    "required": ["Analysis","action_intent"],
    "additionalProperties": False
}

# 情感表达分析的JSON schema
emotion_schema = {
    "type": "object",
    "properties": {
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of does CommentonComment meet the definition of Expressing Feelings"
        },
        "ExpressingFeelings": {
            "type": "boolean",
            "description": "Whether the CommentonComment meets the definition of Expressing Feelings"
        }
    },
    "required": ["Analysis","ExpressingFeelings"],
    "additionalProperties": False
}

# 立场类分析的JSON schema
stance_schema = {
    "type": "object",
    "properties": {
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of does CommentonComment meet the definition of Assertion Persuasion,Affiliating and Supporting or Dissenting and Conflicting"
        },
        "stance_intent": {
            "type": "string",
            "enum": ["Assertion Persuasion", "Affiliating and Supporting", "Dissenting and Conflicting","none"],
            "description": "The intent of the CommentonComment, if the CommentonComment does not meet the definition of Assertion Persuasion,Affiliating and Supporting and Dissenting and Conflicting, return 'none'"
        }
    },
    "required": ["Analysis","stance_intent"],
    "additionalProperties": False
}

schema = {
    "type": "object",
    "properties": {
        "Reasoning": {
            "type": "string",
            "description": "Detailed analysis of which intent CommentonComment belongs to among the following intents according to the analysis of Action,Emotion,Stance and the definition of each intent"
        },
        "Intent": {
            "type": "string",
            "enum": ["Expressing Feelings", "Sharing Information", "Assertion Persuasion", 
                    "Affiliating and Supporting", "Dissenting and Conflicting", "Requesting Information", 
                    "Calling For Action","Unclear Intent"],
            "description": "The final determined intent"
        }
    },
    "required": ["Reasoning","Intent"],
    "additionalProperties": False
}

for index, row in df.iterrows():
    print(f"\n=== 处理第 {index + 1} 行数据 ===")
    
    # 从CSV中读取数据
    post_content = row['原始帖子']
    comment_on_post = row['上下文']
    comment_on_comment = row['用户文本']
    true_label = row['帖文B意图']  # 真实标签
    
    print(f"Post: {post_content}")
    print(f"Comment on Post: {comment_on_post}")
    print(f"Comment on Comment: {comment_on_comment}")
    print(f"True Label: {true_label}")
    
    # 存储真实标签（转换为英文）
    true_label_en = label_mapping.get(true_label, true_label)
    ground_truth.append(true_label_en)
    

    action_params = SamplingParams(
        guided_decoding=GuidedDecodingParams(json=action_schema),
        temperature=0,
        top_p=1,
        max_tokens=1024
    )
    action_prompt = f"""Your task is to give your detailed analysis of does CommentonComment meet the definition of Sharing Information，Requesting Information or Calling For Action.Please think step by step from the perspective of subjective and objective.
    Sharing Information (信息分享)定义 ：提供事实或知识传递客观信息。
    Requesting Information (寻求信息)定义 ：通过提问或请求获取信息或他人观点。
    Calling For Action (号召行动)定义 ：指令或倡议促使他人采取行动。

    Context:
    Post: {post_content}
    CommentonPost: {comment_on_post}
    CommentonComment: {comment_on_comment}

    Output format:
    {{
        "Analysis": "Detailed analysis of does CommentonComment meet the definition of Sharing Information，Requesting Information or Calling For Action",
        "action_intent": "Sharing Information" or "Requesting Information" or "Calling For Action" or "none"
    }}
    """

    action_outputs = llm.generate(
        prompts=action_prompt,
        sampling_params=action_params,
    )

    action_text = action_outputs[0].outputs[0].text
    print("Action Analysis JSON:")
    print(action_text)

    try:
        action_data = json.loads(action_text)
        print("\nAction Analysis Result:")
        print(json.dumps(action_data, indent=2, ensure_ascii=False))
    except json.JSONDecodeError as e:
        print(f"\nAction Analysis JSON parsing error: {e}")
        action_data = None

    emotion_params = SamplingParams(
        guided_decoding=GuidedDecodingParams(json=emotion_schema),
        temperature=0,
        top_p=1,
        max_tokens=1024
    )
    emotion_prompt = f"""Your task is to give your detailed analysis of does CommentonComment meet the definition of Expressing Feelings,Please think step by step from the perspective of subjective and objective.
    ExpressingFeelings (情感表达)定义 ：直接通过语言表露说话者的情感状态。

    Context:
    Post: {post_content}
    CommentonPost: {comment_on_post}
    CommentonComment: {comment_on_comment}

    Output format:
    {{
        "Analysis": "Detailed analysis of does CommentonComment meet the definition of Expressing Feelings",
        "ExpressingFeelings": true/false
    }}
    """

    emotion_outputs = llm.generate(
        prompts=emotion_prompt,
        sampling_params=emotion_params,
    )

    emotion_text = emotion_outputs[0].outputs[0].text
    print("Emotion Analysis JSON:")
    print(emotion_text)

    try:
        emotion_data = json.loads(emotion_text)
        print("\nEmotion Analysis Result:")
        print(json.dumps(emotion_data, indent=2, ensure_ascii=False))
    except json.JSONDecodeError as e:
        print(f"\nEmotion Analysis JSON parsing error: {e}")
        emotion_data = None

    stance_params = SamplingParams(
        guided_decoding=GuidedDecodingParams(json=stance_schema),
        temperature=0,
        top_p=1,
        max_tokens=1024
    )
    stance_prompt = f"""Your task is to give your detailed analysis of does CommentonComment meet the definition of Assertion Persuasion,Affiliating and Supporting or Dissenting and Conflicting,please think step by step from the perspective of subjective and objective.
    Assertion Persuasion (表达主张)定义 ：表达观点或明确立场，展示说话者的判断。
    Affiliating and Supporting (认同与联结)定义 ：表达对他人观点或立场的认同或支持。
    Dissenting and Conflicting (分歧与冲突)定义 ：表达对他人观点或立场的反对或冲突。

    Context:
    Post: {post_content}
    CommentonPost: {comment_on_post}
    CommentonComment: {comment_on_comment}

    Output format:
    {{
        "Analysis": "Detailed analysis of does CommentonComment meet the definition of Assertion Persuasion,Affiliating and Supporting or Dissenting and Conflicting",
        "stance_intent": "Assertion Persuasion" or "Affiliating and Supporting" or "Dissenting and Conflicting" or "none"
    }}
    """

    stance_outputs = llm.generate(
        prompts=stance_prompt,
        sampling_params=stance_params,
    )

    stance_text = stance_outputs[0].outputs[0].text
    print("Stance Analysis JSON:")
    print(stance_text)

    try:
        stance_data = json.loads(stance_text)
        print("\nStance Analysis Result:")
        print(json.dumps(stance_data, indent=2, ensure_ascii=False))
    except json.JSONDecodeError as e:
        print(f"\nStance Analysis JSON parsing error: {e}")
        stance_data = None

    params = SamplingParams(
        guided_decoding=GuidedDecodingParams(json=schema),
        temperature=0,
        top_p=1,
        max_tokens=1024
    )
    prompt = f"""You are an expert of intent,please give your detailed analysis of which intent CommentonComment belongs to among the following intents according to the analysis of Action,Emotion,Stance and the definition of each intent.Please think step by step from the perspective of subjective and objective.
    Expressing Feelings (情感表达)定义 ：直接通过语言表露说话者的情感状态。
    Sharing Information (信息分享)定义 ：提供事实或知识传递客观信息。
    Assertion Persuasion (表达主张)定义 ：表达观点或明确立场，展示说话者的判断。
    Affiliating and Supporting (认同与联结)定义 ：表达对他人观点或立场的认同或支持。
    Dissenting and Conflicting (分歧与冲突)定义 ：表达对他人观点或立场的反对或冲突。
    Requesting Information (寻求信息)定义 ：通过提问或请求获取信息或他人观点。
    Calling For Action (号召行动)定义 ：指令或倡议促使他人采取行动。
    Unclear Intent (模糊意图)定义 ：无法确定意图。

    Context:
    Post: {post_content}
    Comment on Post: {comment_on_post}
    Comment on Comment: {comment_on_comment}

    Preliminary Action Analysis: {action_data}
    Preliminary Emotion Analysis: {emotion_data}
    Preliminary Stance Analysis: {stance_data}

    Output format:
    {{
        "Reasoning": "Detailed analysis of which intent CommentonComment belongs to among the following intents according to the analysis of Action,Emotion,Stance and the definition of each intent",
        "Intent": "Expressing Feelings" or "Sharing Information" or "Assertion Persuasion" or "Affiliating and Supporting" or "Dissenting and Conflicting" or "Requesting Information" or "Calling For Action" or "Unclear Intent"
    }}
    """

    outputs = llm.generate(
        prompts=prompt,
        sampling_params=params,
    )

    text = outputs[0].outputs[0].text
    print("Intent Analysis JSON:")
    print(text)

    try:
        data = json.loads(text)
        predicted_label=data['Intent']
        print("\nIntent Analysis Result:") 
        predictions.append(predicted_label)
        print(f"预测意图: {predicted_label}")
        print(f"真实意图: {true_label_en}")
        print(f"预测正确: {'✓' if predicted_label == true_label_en else '✗'}")
        print(f"推理过程: {data['Reasoning']}")
        print(json.dumps(data, indent=2, ensure_ascii=False))
    except json.JSONDecodeError as e:
        print(f"\nIntent JSON parsing error: {e}")
        predictions.append('Unclear Intent')


# 计算评估指标
print("\n" + "="*80)
print("=== 评估结果 ===")
print("="*80)

# 确保预测结果和真实标签数量一致
if len(predictions) != len(ground_truth):
    print(f"警告: 预测结果数量({len(predictions)})与真实标签数量({len(ground_truth)})不一致")
    min_len = min(len(predictions), len(ground_truth))
    predictions = predictions[:min_len]
    ground_truth = ground_truth[:min_len]

# 计算各项指标
accuracy = accuracy_score(ground_truth, predictions)
f1_macro = f1_score(ground_truth, predictions, average='macro', zero_division=0)
f1_micro = f1_score(ground_truth, predictions, average='micro', zero_division=0)
f1_weighted = f1_score(ground_truth, predictions, average='weighted', zero_division=0)
precision_macro = precision_score(ground_truth, predictions, average='macro', zero_division=0)
recall_macro = recall_score(ground_truth, predictions, average='macro', zero_division=0)

print(f"样本总数: {len(ground_truth)}")
print(f"准确率 (Accuracy): {accuracy:.4f}")
print(f"F1分数 (Macro): {f1_macro:.4f}")
print(f"F1分数 (Micro): {f1_micro:.4f}")
print(f"F1分数 (Weighted): {f1_weighted:.4f}")
print(f"精确率 (Precision Macro): {precision_macro:.4f}")
print(f"召回率 (Recall Macro): {recall_macro:.4f}")

print("\n=== 详细分类报告 ===")
print(classification_report(ground_truth, predictions, target_names=None, zero_division=0))

print("\n=== 混淆矩阵 ===")
cm = confusion_matrix(ground_truth, predictions)
unique_labels = sorted(list(set(ground_truth + predictions)))
print("标签顺序:", unique_labels)
print(cm)

# 保存结果到文件
results_df = pd.DataFrame({
    'index': range(len(ground_truth)),
    'ground_truth': ground_truth,
    'predictions': predictions,
    'correct': [gt == pred for gt, pred in zip(ground_truth, predictions)]
})
results_df.to_csv('aes_evaluation_results.csv', index=False, encoding='utf-8')
print(f"\n详细结果已保存到 aes_evaluation_results.csv")

# 计算每个类别的准确率
print("\n=== 各类别准确率 ===")
for label in unique_labels:
    label_mask = np.array(ground_truth) == label
    if label_mask.sum() > 0:
        label_accuracy = accuracy_score(
            np.array(ground_truth)[label_mask], 
            np.array(predictions)[label_mask]
        )
        print(f"{label}: {label_accuracy:.4f} ({label_mask.sum()} 样本)")

print("\n=== 预测标签分布 ===")
pred_counts = pd.Series(predictions).value_counts()
print(pred_counts)

print("\n=== 真实标签分布 ===")
true_counts = pd.Series(ground_truth).value_counts()
print(true_counts)