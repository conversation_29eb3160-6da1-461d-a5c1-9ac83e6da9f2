# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

import os
# 设置使用 Hugging Face 国内镜像
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'  # 或者使用其他可用的国内镜像

from vllm import LLM, SamplingParams


def main():
    # Create an LLM.
    llm = LLM(model="Qwen/Qwen2.5-7B-Instruct")
    # Create a sampling params object.
    sampling_params = SamplingParams(temperature=0, top_p=1, n=1, max_tokens=256)
    Post = "#CWSJ头条 美中同意大幅降低关税：美国将对华加征的关税削减至30%，中国则将对美关税下调至10%，同时双方计划展开进一步的贸易谈判。"
    CommentonPost = "可以的，一赢各表� 目前美对中：四月前+10%（强调三月的20%芬太尼关税保留，也就是30%） 中对美：四月前+10%（三月对芬太尼关税的反制没说取消，默默收钱） 川普可以给maga交代：30%对10%，咱们赢了。老中也可以说：10%对10%，咱们赢了。大家一起赚钱，一起有面子"
    CommentonComment= "老中实诚人，不玩这种文字游戏。"


    grammarPrompt = [
    f'''
    "IDENTITY and PURPOSE": "You are an grammar expert specialized in completing grammar elements of Post based on your expertise,completing grammar elements of CommentonPost based on your expertise and Post,completing grammar elements of CommentonComment based on your expertise,Post and CommentonPost.",
    "TASK": "For Post:{Post},use your expertise to fill in the missing grammatical parts so that it is a complete sentence.
    For CommentonPost:{CommentonPost},use your expertise and Post to fill in the missing grammatical parts so that it is a complete sentence.
    For CommentonComment:{CommentonComment},use your expertise,Post and CommentonPost to fill in the missing grammatical parts so that it is a complete sentence.",
    "Note": "You MUST output in strict JSON format only, without any additional text,output must be a complete, parseable JSON object.
    Only output the JSON in the format of OUTPUT, without any additional text before or after the json.",
    "INPUT": "Social media data: Post:{Post} CommentonPost:{CommentonPost} CommentonComment: {CommentonComment}",
    "OUTPUT": 
    "{{
        "Post": ["The revised Post content"],
        "CommentonPost": ["The revised CommentonPost content"],
        "CommentonComment": ["The revised CommentonComment content"]
    }}",
    "INPUT_EXAMPLE": "Social media data: Post:#CWSJ头条 美中同意大幅降低关税：美国将对华加征的关税削减至30%，中国则将对美关税下调至10%，同时双方计划展开进一步的贸易谈判。CommentonPost:二月初反制芬太尼关税：对煤炭、液化天然气加征15%关税，对原油、农业机械、大排量汽车、皮卡加征10％关税。三月初反制芬太尼关税：对鸡肉、小麦、玉米、棉花加征15%关税；对高粱、大豆、猪肉、牛肉、水产品、水果、蔬菜、乳制品加征10%关税。 CommentonComment: 不提就是没有。",
    "OUTPUT_EXAMPLE": "{{
        "Post": ["#CWSJ头条 美中同意大幅降低关税：美国将对华加征的关税削减至30%，中国则将对美关税下调至10%，同时双方计划展开进一步的贸易谈判。"],
        "CommentonPost": ["二月初（中国）反制芬太尼关税：对煤炭、液化天然气加征15%关税，对原油、农业机械、大排量汽车、皮卡加征10％关税。三月初（中国）反制芬太尼关税：对鸡肉、小麦、玉米、棉花加征15%关税；对高粱、大豆、猪肉、牛肉、水产品、水果、蔬菜、乳制品加征10%关税。"],
        "CommentonComment": ["（美国）不提就是没有。"]
    }}",
    '''
    ]

    # Generate texts from the prompts.
    # The output is a list of RequestOutput objects
    # that contain the prompt, generated text, and other information.
    outputs = llm.generate(grammarPrompt, sampling_params)
    # Print the outputs.
    print("\nGenerated Outputs:\n" + "-" * 60)
    for output in outputs:
        prompt = output.prompt
        generated_text = output.outputs[0].text
        print(f"Prompt:    {prompt!r}")
        print(f"Output:    {generated_text!r}")
        print("-" * 60)


if __name__ == "__main__":
    main()