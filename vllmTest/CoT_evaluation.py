import os
import json
import warnings
import pandas as pd
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, classification_report, confusion_matrix
import numpy as np

# 设置使用1号GPU
os.environ['CUDA_VISIBLE_DEVICES'] = '1'

warnings.filterwarnings('ignore')

# 设置环境变量
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

from vllm import LLM, SamplingParams
from vllm.sampling_params import GuidedDecodingParams

# 读取CSV文件
df = pd.read_csv('美中关税战争_筛选结果.csv')

# 初始化LLM
llm = LLM(model="Qwen/Qwen2.5-7B-Instruct")

# 存储预测结果和真实标签
predictions = []
ground_truth = []

# 标签映射字典（将中文标签映射为英文）
label_mapping = {
    '情感表达': 'Expressing Feelings',
    '信息分享': 'Sharing Information', 
    '表达主张': 'Assertion Persuasion',
    '认同与联结': 'AffiliatingSupporting',
    '分歧与冲突': 'DissentingConflicting',
    '寻求信息': 'RequestingInformation',
    '号召行动': 'CallingForAction',
    '模糊意图': 'UnclearIntent'
}

schema = {
    "type": "object",
    "properties": {
        "Reasoning": {
            "type": "string",
            "description": "Detailed reasoning for the judgment"
        },
        "Intent": {
            "type": "string",
            "enum": ["ExpressingFeelings", "SharingSubjectiveInformation", "AssertionPersuasion", 
                    "AffiliatingSupporting", "DissentingConflicting", "RequestingInformation", 
                    "CallingForAction","UnclearIntent"],
            "description": "The final determined intent"
        }
    },
    "required": ["Reasoning","Intent"],
    "additionalProperties": False
}



# 处理每一行数据
for index, row in df.iterrows():
    print(f"\n=== 处理第 {index + 1} 行数据 ===")
    
    # 从CSV中读取数据
    post_content = row['原始帖子']
    comment_on_post = row['上下文']
    comment_on_comment = row['用户文本']
    true_label = row['帖文B意图']  # 真实标签
    
    print(f"Post: {post_content}")
    print(f"Comment on Post: {comment_on_post}")
    print(f"Comment on Comment: {comment_on_comment}")
    print(f"True Label: {true_label}")
    
    # 存储真实标签（转换为英文）
    true_label_en = label_mapping.get(true_label, true_label)
    ground_truth.append(true_label_en)
    
    # 创建采样参数
    sampling_params = SamplingParams(
        guided_decoding=GuidedDecodingParams(json=schema),
        temperature=0,
        top_p=1,
        max_tokens=1024
    )
    # 构建prompt
    prompt = f"""Your task is to determine the intention of the CommentonComment by assigning one of the following labels,Let's think step by step.:

1. ExpressingFeelings (情感表达): 直接通过语言表露说话者的情感状态
2. SharingSubjectiveInformation (信息分享): 提供事实或知识传递客观信息
3. AssertionPersuasion (表达主张): 表达观点或明确立场，展示说话者的判断
4. AffiliatingSupporting (认同与联结): 表达对他人观点或行为的积极态度，如认同、支持、感谢
5. DissentingConflicting (分歧与冲突): 表达对他人观点或行为的否定、批评、讽刺
6. RequestingInformation (寻求信息): 通过提问或请求获取信息或他人观点
7. CallingForAction (号召行动): 指令或倡议促使他人采取行动
8. UnclearIntent (模糊意图): 意图不明确

Context:
Post: {post_content}
CommentonPost: {comment_on_post}
CommentonComment: {comment_on_comment}

输出格式：
    {{
        "Reasoning": "详细分析CommentonComment为什么体现的是这个意图，以及为什么不是其他意图",
        "Intent": "选择的最终意图"
    }}
    """

    try:
        # 生成预测结果
        outputs = llm.generate(
            prompts=prompt,
            sampling_params=sampling_params,
                            )
        generated_text = outputs[0].outputs[0].text
        
        print(f"Generated Output: {generated_text}")
        
        final_data = json.loads(generated_text)
        predicted_label = final_data['Intent']
        predictions.append(predicted_label)
        
        print(f"预测意图: {predicted_label}")
        print(f"真实意图: {true_label_en}")
        print(f"预测正确: {'✓' if predicted_label == true_label_en else '✗'}")
        print(f"推理过程: {final_data['Reasoning']}")
        print(json.dumps(final_data, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"处理第 {index + 1} 行数据时出错: {e}")
        predictions.append('UnclearIntent')  # 处理失败时使用默认标签

# 计算评估指标
print("\n" + "="*80)
print("=== 评估结果 ===")
print("="*80)

# 确保预测结果和真实标签数量一致
if len(predictions) != len(ground_truth):
    print(f"警告: 预测结果数量({len(predictions)})与真实标签数量({len(ground_truth)})不一致")
    min_len = min(len(predictions), len(ground_truth))
    predictions = predictions[:min_len]
    ground_truth = ground_truth[:min_len]

# 计算各项指标
accuracy = accuracy_score(ground_truth, predictions)
f1_macro = f1_score(ground_truth, predictions, average='macro', zero_division=0)
f1_micro = f1_score(ground_truth, predictions, average='micro', zero_division=0)
f1_weighted = f1_score(ground_truth, predictions, average='weighted', zero_division=0)
precision_macro = precision_score(ground_truth, predictions, average='macro', zero_division=0)
recall_macro = recall_score(ground_truth, predictions, average='macro', zero_division=0)

print(f"样本总数: {len(ground_truth)}")
print(f"准确率 (Accuracy): {accuracy:.4f}")
print(f"F1分数 (Macro): {f1_macro:.4f}")
print(f"F1分数 (Micro): {f1_micro:.4f}")
print(f"F1分数 (Weighted): {f1_weighted:.4f}")
print(f"精确率 (Precision Macro): {precision_macro:.4f}")
print(f"召回率 (Recall Macro): {recall_macro:.4f}")

print("\n=== 详细分类报告 ===")
print(classification_report(ground_truth, predictions, target_names=None, zero_division=0))

print("\n=== 混淆矩阵 ===")
cm = confusion_matrix(ground_truth, predictions)
unique_labels = sorted(list(set(ground_truth + predictions)))
print("标签顺序:", unique_labels)
print(cm)

# 保存结果到文件
results_df = pd.DataFrame({
    'index': range(len(ground_truth)),
    'ground_truth': ground_truth,
    'predictions': predictions,
    'correct': [gt == pred for gt, pred in zip(ground_truth, predictions)]
})
results_df.to_csv('baseline_evaluation_results.csv', index=False, encoding='utf-8')
print(f"\n详细结果已保存到 baseline_evaluation_results.csv")

# 计算每个类别的准确率
print("\n=== 各类别准确率 ===")
for label in unique_labels:
    label_mask = np.array(ground_truth) == label
    if label_mask.sum() > 0:
        label_accuracy = accuracy_score(
            np.array(ground_truth)[label_mask], 
            np.array(predictions)[label_mask]
        )
        print(f"{label}: {label_accuracy:.4f} ({label_mask.sum()} 样本)")

print("\n=== 预测标签分布 ===")
pred_counts = pd.Series(predictions).value_counts()
print(pred_counts)

print("\n=== 真实标签分布 ===")
true_counts = pd.Series(ground_truth).value_counts()
print(true_counts)