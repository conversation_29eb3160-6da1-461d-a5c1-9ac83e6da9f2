#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import requests
import json
import time
import re

class DeepSeekAPI:
    """DeepSeek API客户端"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def generate_label(self, text: str) -> str:
        """
        使用DeepSeek API将文本转换为机器学习标签
        """
        if not text or not text.strip():
            return ""
        
        # 构建提示词
        prompt = f"""
请将以下文本进行语义压缩，生成一个简洁的标签。要求：
1. 提取核心语义概念，高度抽象化
2. 使用1-3个关键词，用下划线连接
3. 去除具体细节，保留本质含义
4. 适合作为机器学习分类标签
5. 使用中文

示例：
- "以行为测试框架回应政治指控" → "行为_回应"
- "发布违法言论" → "违法_言论"
- "将个人困境转化为公共话语" → "困境_转化"

原文本：{text}

压缩标签：
"""
        
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "temperature": 0.3,
            "max_tokens": 50
        }
        
        try:
            response = requests.post(
                self.base_url, 
                headers=self.headers, 
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                label = result['choices'][0]['message']['content'].strip()
                # 清理标签，去除多余的格式
                label = re.sub(r'^标签[：:]\s*', '', label)
                label = re.sub(r'^Label[：:]\s*', '', label, flags=re.IGNORECASE)
                return label
            else:
                print(f"API请求失败: {response.status_code}, {response.text}")
                return text  # 如果API失败，返回原文本
                
        except Exception as e:
            print(f"API调用异常: {str(e)}")
            return text  # 如果API失败，返回原文本

def main():
    # 配置参数
    api_key = "sk-2ee194238a9c4a5486709bd13d9a8ffc"
    input_file = "simplified_motivations_mapping_cleaned.xlsx"
    output_file = "simplified_motivations_mapping_final.xlsx"
    
    # 读取清理后的Excel文件
    try:
        df = pd.read_excel(input_file)
        print(f"成功读取清理后的Excel文件，共 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")
    except Exception as e:
        print(f"读取Excel文件失败: {str(e)}")
        return
    
    # 初始化DeepSeek API
    api_client = DeepSeekAPI(api_key)
    
    # 创建E列用于存储标签
    df['E'] = ""
    
    # 处理每一行数据
    processed_count = 0
    for index, row in df.iterrows():
        print(f"处理第 {index + 1}/{len(df)} 行...")
        
        # 优先使用清理后的数据
        text_to_process = ""
        if pd.notna(row.get('new_label_cleaned')) and isinstance(row['new_label_cleaned'], str) and row['new_label_cleaned'].strip():
            text_to_process = str(row['new_label_cleaned']).strip()
        elif pd.notna(row.get('original_cleaned')) and isinstance(row['original_cleaned'], str) and row['original_cleaned'].strip():
            text_to_process = str(row['original_cleaned']).strip()
        elif pd.notna(row.get('new_label')) and isinstance(row['new_label'], str) and row['new_label'].strip():
            text_to_process = str(row['new_label']).strip()
        elif pd.notna(row.get('original')) and isinstance(row['original'], str) and row['original'].strip():
            text_to_process = str(row['original']).strip()
        
        if text_to_process:
            # 生成标签
            label = api_client.generate_label(text_to_process)
            df.at[index, 'E'] = label
            processed_count += 1
            print(f"  原文本: {text_to_process[:30]}...")
            print(f"  生成标签: {label}")
        else:
            print(f"  跳过空行")
        
        # 添加延迟避免API限制
        time.sleep(1)
        
        # 每处理10行保存一次，防止数据丢失
        if processed_count % 10 == 0:
            try:
                df.to_excel(output_file, index=False)
                print(f"  已保存进度到 {output_file}")
            except Exception as e:
                print(f"  保存进度失败: {str(e)}")
    
    # 最终保存结果
    try:
        df.to_excel(output_file, index=False)
        print(f"\n处理完成！共处理 {processed_count} 行数据")
        print(f"结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存文件失败: {str(e)}")

if __name__ == "__main__":
    main()
