#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件处理脚本
功能：
1. 接入DeepSeek API
2. 处理simplified_motivations_mapping.xlsx文件
3. 去除括号和括号内容
4. 将剩余内容压缩成机器学习标签形式
5. 将结果放在E列
"""

import pandas as pd
import re
import requests
import json
import time
from typing import List, Optional

class DeepSeekAPI:
    """DeepSeek API客户端"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def generate_label(self, text: str) -> str:
        """
        使用DeepSeek API将文本转换为机器学习标签
        """
        # 先去除括号和括号内容
        cleaned_text = self.remove_brackets(text)
        
        if not cleaned_text.strip():
            return ""
        
        # 构建提示词
        prompt = f"""
请将以下文本转换为简洁的机器学习标签格式。要求：
1. 保留核心含义
2. 使用简短的词汇或短语
3. 适合作为分类标签
4. 如果是多个概念，用下划线连接
5. 使用中文都可以，但要保持一致性


原文本：{cleaned_text}

标签：
"""
        
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "temperature": 0.3,
            "max_tokens": 100
        }
        
        try:
            response = requests.post(
                self.base_url, 
                headers=self.headers, 
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                label = result['choices'][0]['message']['content'].strip()
                # 清理标签，去除多余的格式
                label = re.sub(r'^标签[：:]\s*', '', label)
                label = re.sub(r'^Label[：:]\s*', '', label, flags=re.IGNORECASE)
                return label
            else:
                print(f"API请求失败: {response.status_code}, {response.text}")
                return cleaned_text  # 如果API失败，返回清理后的原文本
                
        except Exception as e:
            print(f"API调用异常: {str(e)}")
            return cleaned_text  # 如果API失败，返回清理后的原文本
    
    @staticmethod
    def remove_brackets(text: str) -> str:
        """
        去除文本中的括号和括号内容
        支持：() （） [] 【】 {} 《》 「」
        """
        if pd.isna(text) or not isinstance(text, str):
            return ""

        # 定义括号对，包括中文和英文括号
        bracket_patterns = [
            r'\([^)]*\)',      # 英文圆括号 ()
            r'（[^）]*）',      # 中文圆括号 （）
            r'\[[^\]]*\]',     # 方括号 []
            r'【[^】]*】',      # 中文方括号 【】
            r'\{[^}]*\}',      # 花括号 {}
            r'《[^》]*》',      # 书名号 《》
            r'「[^」]*」',      # 日式引号 「」
        ]

        result = text
        for pattern in bracket_patterns:
            result = re.sub(pattern, '', result)

        # 清理多余的空格
        result = re.sub(r'\s+', ' ', result).strip()
        return result

def clean_brackets_in_dataframe(df):
    """
    清理DataFrame中指定列的括号内容
    """
    print("开始清理括号内容...")

    # 处理original列
    if 'original' in df.columns:
        df['original'] = df['original'].apply(lambda x: DeepSeekAPI.remove_brackets(x) if pd.notna(x) else x)
        print("已清理original列的括号内容")

    # 处理new_label列
    if 'new_label' in df.columns:
        df['new_label'] = df['new_label'].apply(lambda x: DeepSeekAPI.remove_brackets(x) if pd.notna(x) else x)
        print("已清理new_label列的括号内容")

    return df

def process_excel_file(file_path: str, api_key: str, output_path: str = None, clean_brackets_only: bool = False):
    """
    处理Excel文件的主函数
    """
    print(f"开始处理文件: {file_path}")

    # 读取Excel文件
    try:
        df = pd.read_excel(file_path)
        print(f"成功读取Excel文件，共 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")
    except Exception as e:
        print(f"读取Excel文件失败: {str(e)}")
        return

    # 第一步：清理括号内容
    df = clean_brackets_in_dataframe(df)

    # 如果只需要清理括号，直接保存并返回
    if clean_brackets_only:
        if output_path is None:
            output_path = file_path.replace('.xlsx', '_cleaned.xlsx')

        try:
            df.to_excel(output_path, index=False)
            print(f"括号清理完成，结果已保存到: {output_path}")
            return output_path
        except Exception as e:
            print(f"保存文件失败: {str(e)}")
            return None

    # 第二步：使用API生成标签
    print("\n开始使用DeepSeek API生成标签...")
    api_client = DeepSeekAPI(api_key)

    # 创建E列用于存储标签
    df['E'] = ""

    # 处理每一行数据
    for index, row in df.iterrows():
        print(f"处理第 {index + 1}/{len(df)} 行...")

        # 优先处理original列，如果为空则处理new_label列
        text_to_process = ""
        if pd.notna(row.get('original')) and isinstance(row['original'], str) and row['original'].strip():
            text_to_process = str(row['original']).strip()
        elif pd.notna(row.get('new_label')) and isinstance(row['new_label'], str) and row['new_label'].strip():
            text_to_process = str(row['new_label']).strip()

        if text_to_process:
            # 生成标签
            label = api_client.generate_label(text_to_process)
            df.at[index, 'E'] = label
            print(f"  原文本: {text_to_process[:50]}...")
            print(f"  生成标签: {label}")
        else:
            print(f"  跳过空行")

        # 添加延迟避免API限制
        time.sleep(1)

    # 保存结果
    if output_path is None:
        output_path = file_path.replace('.xlsx', '_processed.xlsx')

    try:
        df.to_excel(output_path, index=False)
        print(f"处理完成，结果已保存到: {output_path}")
        return output_path
    except Exception as e:
        print(f"保存文件失败: {str(e)}")
        return None

def main():
    """主函数"""
    # 配置参数
    api_key = "sk-2ee194238a9c4a5486709bd13d9a8ffc"
    input_file = "simplified_motivations_mapping.xlsx"

    # 第一步：只清理括号内容
    print("=== 第一步：清理括号内容 ===")
    cleaned_file = process_excel_file(input_file, api_key,
                                    output_path="simplified_motivations_mapping_cleaned.xlsx",
                                    clean_brackets_only=True)

    if cleaned_file:
        print(f"\n括号清理完成！清理后的文件：{cleaned_file}")

        # 询问是否继续进行API处理
        user_input = input("\n是否继续使用DeepSeek API生成标签？(y/n): ").strip().lower()

        if user_input == 'y' or user_input == 'yes':
            print("\n=== 第二步：使用API生成标签 ===")
            final_file = process_excel_file(cleaned_file, api_key,
                                          output_path="simplified_motivations_mapping_final.xlsx",
                                          clean_brackets_only=False)
            if final_file:
                print(f"\n全部处理完成！最终文件：{final_file}")
        else:
            print("已完成括号清理，跳过API处理。")
    else:
        print("括号清理失败！")

if __name__ == "__main__":
    main()
