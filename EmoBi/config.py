# vLLM 离线部署配置
MODEL_NAME = "meta-llama/Llama-2-7b-chat-hf"  # 或者使用其他支持的模型

# vLLM 推理参数
TEMPERATURE = 0.0
MAX_TOKENS = 1024

# vLLM 硬件配置
GPU_MEMORY_UTILIZATION = 0.9  # GPU内存使用率 (0.0-1.0)
TENSOR_PARALLEL_SIZE = 1       # 张量并行大小，多GPU时可以增加

# 结果目录
RESULTS_DIR = ".."

# 支持的模型列表 (可选择使用)
SUPPORTED_MODELS = [
    "meta-llama/Llama-2-7b-chat-hf",
    "meta-llama/Llama-2-13b-chat-hf",
    "meta-llama/Meta-Llama-3-8B-Instruct",
    "microsoft/DialoGPT-medium",
    "Qwen/Qwen-7B-Chat",
    # 添加更多支持的模型...
]