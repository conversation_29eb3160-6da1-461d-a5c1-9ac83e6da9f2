# EmoBi

This repository contains the source code for our paper "Enhancing Hyperbole and Metaphor Detection with Their Bidirectional Dynamic Interaction and Emotion Knowledge".

**Note: This version has been updated to use vLLM for offline local model deployment instead of Ollama.**

## Setup

### 1. Build Environment
```bash
cd EmoBi
# use anaconda to build environment
conda create -n EmoBi python=3.10
conda activate EmoBi
# install packages
pip install -r requirements.txt
```

### 2. Model Configuration

Edit `config.py` to configure your model and hardware settings:

```python
# 模型配置
MODEL_NAME = "meta-llama/Llama-2-7b-chat-hf"  # 选择你要使用的模型

# 推理参数
TEMPERATURE = 0.0      # 温度参数
MAX_TOKENS = 1024      # 最大生成token数

# 硬件配置
GPU_MEMORY_UTILIZATION = 0.9  # GPU内存使用率 (0.0-1.0)
TENSOR_PARALLEL_SIZE = 1       # 张量并行大小，多GPU时可以增加
```

### 3. Supported Models

The following models are tested and supported:
- `meta-llama/Llama-2-7b-chat-hf` (推荐，默认)
- `meta-llama/Llama-2-13b-chat-hf`
- `meta-llama/Meta-Llama-3-8B-Instruct`
- `microsoft/DialoGPT-medium`
- `Qwen/Qwen-7B-Chat`

You can also use other vLLM-compatible models by updating the `MODEL_NAME` in `config.py`.

## Quick Start

**直接运行主程序** (无需启动额外服务器):
```bash
python main.py
```

第一次运行时，vLLM会自动下载并加载指定的模型，这可能需要一些时间。

## Hardware Requirements

### Minimum Requirements:
- **GPU**: NVIDIA GPU with at least 8GB VRAM (for 7B models)
- **RAM**: 16GB system RAM
- **Storage**: 20GB free space for model weights

### Recommended Requirements:
- **GPU**: NVIDIA GPU with 16GB+ VRAM (for 13B+ models)
- **RAM**: 32GB+ system RAM
- **Storage**: 50GB+ free space

### Multi-GPU Setup:
If you have multiple GPUs, you can enable tensor parallelism by updating `config.py`:
```python
TENSOR_PARALLEL_SIZE = 2  # 对于2个GPU
# 或
TENSOR_PARALLEL_SIZE = 4  # 对于4个GPU
```

## Migration from Ollama

This codebase has been migrated from Ollama to vLLM offline deployment. Key changes:

- **Dependencies**: Added `vllm` to requirements.txt, removed `openai` and `requests`
- **Model Interface**: Replaced `ollama.chat()` calls with direct vLLM inference
- **Configuration**: Updated config.py with vLLM offline deployment settings
- **Deployment**: Models are now loaded directly in memory, no API server needed

## Troubleshooting

### Common Issues:

1. **CUDA Out of Memory**:
   - Reduce `GPU_MEMORY_UTILIZATION` in config.py (try 0.8 or 0.7)
   - Use a smaller model (e.g., 7B instead of 13B)
   - Enable tensor parallelism if you have multiple GPUs

2. **Model Download Issues**:
   - Ensure stable internet connection for first-time model download
   - Check if you have sufficient disk space
   - Some models may require Hugging Face authentication

3. **Slow Inference**:
   - Ensure you're using GPU (check with `nvidia-smi`)
   - Increase `GPU_MEMORY_UTILIZATION` if you have spare VRAM
   - Consider using a smaller model for faster inference

4. **Import Errors**:
   - Make sure all dependencies are installed: `pip install -r requirements.txt`
   - Check CUDA compatibility with your PyTorch installation

### Performance Tips:

- **First Run**: Model loading takes time on first run, subsequent runs are faster
- **Batch Processing**: The current implementation processes one sentence at a time, but vLLM supports batch processing for better throughput
- **Model Choice**: Smaller models (7B) are faster but may be less accurate than larger models (13B+)
