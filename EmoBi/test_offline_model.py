#!/usr/bin/env python3
"""
测试vLLM离线部署的简单脚本
在运行主程序之前，使用此脚本验证模型是否能正常加载和推理
"""

import sys
import time
from config import MODEL_NAME, TEMPERATURE, MAX_TOKENS, GPU_MEMORY_UTILIZATION, TENSOR_PARALLEL_SIZE

def test_model_loading():
    """测试模型加载"""
    try:
        print("=== vLLM离线模型测试 ===")
        print(f"模型: {MODEL_NAME}")
        print(f"GPU内存使用率: {GPU_MEMORY_UTILIZATION}")
        print(f"张量并行大小: {TENSOR_PARALLEL_SIZE}")
        print(f"温度: {TEMPERATURE}")
        print(f"最大tokens: {MAX_TOKENS}")
        print()
        
        print("正在加载vLLM...")
        from vllm import LLM, SamplingParams
        
        print(f"正在初始化模型: {MODEL_NAME}")
        print("注意: 首次运行可能需要下载模型，请耐心等待...")
        
        start_time = time.time()
        
        # 初始化vLLM
        llm = LLM(
            model=MODEL_NAME,
            gpu_memory_utilization=GPU_MEMORY_UTILIZATION,
            tensor_parallel_size=TENSOR_PARALLEL_SIZE,
            trust_remote_code=True
        )
        
        load_time = time.time() - start_time
        print(f"✓ 模型加载成功! 耗时: {load_time:.2f}秒")
        
        return llm
        
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return None

def test_inference(llm):
    """测试模型推理"""
    try:
        print("\n=== 推理测试 ===")
        
        # 设置采样参数
        sampling_params = SamplingParams(
            temperature=TEMPERATURE,
            max_tokens=MAX_TOKENS,
            stop=None
        )
        
        # 测试消息
        test_message = "Hello, how are you today?"
        print(f"测试输入: {test_message}")
        
        # 格式化prompt（针对聊天模型）
        if "chat" in MODEL_NAME.lower() or "instruct" in MODEL_NAME.lower():
            formatted_prompt = f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\n{test_message}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
        else:
            formatted_prompt = test_message
        
        print("正在生成响应...")
        start_time = time.time()
        
        # 生成响应
        outputs = llm.generate([formatted_prompt], sampling_params)
        
        inference_time = time.time() - start_time
        
        # 提取结果
        generated_text = outputs[0].outputs[0].text.strip()
        
        print(f"✓ 推理成功! 耗时: {inference_time:.2f}秒")
        print(f"模型响应: {generated_text}")
        
        return True
        
    except Exception as e:
        print(f"✗ 推理测试失败: {e}")
        return False

def test_emotion_analysis(llm):
    """测试情感分析功能"""
    try:
        print("\n=== 情感分析测试 ===")
        
        # 设置采样参数
        sampling_params = SamplingParams(
            temperature=TEMPERATURE,
            max_tokens=MAX_TOKENS,
            stop=None
        )
        
        # 测试句子
        test_sentence = "I am extremely happy today!"
        message = f"""
        Analyze the emotion valence of the following sentence and explain it. 
        Sentence: {test_sentence}
        Output format:
        Emotion analysis reason: <brief reason>
        """
        
        print(f"测试句子: {test_sentence}")
        
        # 格式化prompt
        if "chat" in MODEL_NAME.lower() or "instruct" in MODEL_NAME.lower():
            formatted_prompt = f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\n{message}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
        else:
            formatted_prompt = message
        
        print("正在分析情感...")
        start_time = time.time()
        
        # 生成响应
        outputs = llm.generate([formatted_prompt], sampling_params)
        
        inference_time = time.time() - start_time
        
        # 提取结果
        result_content = outputs[0].outputs[0].text.strip()
        
        print(f"✓ 情感分析完成! 耗时: {inference_time:.2f}秒")
        print(f"分析结果: {result_content}")
        
        # 尝试解析结果
        if "Emotion analysis reason:" in result_content:
            emotion_reason = result_content.split("Emotion analysis reason:")[1].strip()
            print(f"✓ 成功提取情感分析原因: {emotion_reason}")
        else:
            print("⚠ 未能按预期格式提取情感分析原因")
        
        return True
        
    except Exception as e:
        print(f"✗ 情感分析测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始vLLM离线部署测试...\n")
    
    # 测试模型加载
    llm = test_model_loading()
    if llm is None:
        print("\n测试失败: 无法加载模型")
        sys.exit(1)
    
    # 测试基本推理
    if not test_inference(llm):
        print("\n测试失败: 基本推理测试失败")
        sys.exit(1)
    
    # 测试情感分析功能
    if not test_emotion_analysis(llm):
        print("\n测试失败: 情感分析测试失败")
        sys.exit(1)
    
    print("\n🎉 所有测试通过! vLLM离线部署工作正常。")
    print("现在可以运行主程序: python main.py")

if __name__ == "__main__":
    main()
