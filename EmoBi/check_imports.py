#!/usr/bin/env python3
"""
检查导入是否正常的简单脚本
"""

def check_imports():
    """检查所有必要的导入"""
    success = True

    print("=== 依赖检查 ===")

    # 检查基础依赖
    try:
        print("检查numpy...")
        import numpy
        print(f"✓ numpy {numpy.__version__}")
    except Exception as e:
        print(f"✗ numpy导入失败: {e}")
        success = False

    try:
        print("检查pandas...")
        import pandas
        print(f"✓ pandas {pandas.__version__}")
    except Exception as e:
        print(f"✗ pandas导入失败: {e}")
        success = False

    try:
        print("检查scikit-learn...")
        import sklearn
        print(f"✓ scikit-learn {sklearn.__version__}")
    except Exception as e:
        print(f"✗ scikit-learn导入失败: {e}")
        print("这可能是numpy版本兼容性问题")
        success = False

    try:
        print("检查torch...")
        import torch
        print(f"✓ torch {torch.__version__}")
    except Exception as e:
        print(f"✗ torch导入失败: {e}")
        success = False

    try:
        print("检查transformers...")
        import transformers
        print(f"✓ transformers {transformers.__version__}")
    except Exception as e:
        print(f"✗ transformers导入失败: {e}")
        success = False

    try:
        print("检查vLLM...")
        import vllm
        print(f"✓ vLLM导入成功")
    except Exception as e:
        print(f"✗ vLLM导入失败: {e}")
        success = False

    if not success:
        print("\n❌ 部分依赖导入失败")
        print("建议运行修复脚本: bash fix_environment.sh")
        return False

    # 检查项目文件
    try:
        print("\n检查配置文件...")
        from config import MODEL_NAME, TEMPERATURE, MAX_TOKENS, GPU_MEMORY_UTILIZATION, TENSOR_PARALLEL_SIZE
        print(f"✓ 配置文件导入成功")
        print(f"  模型: {MODEL_NAME}")
        print(f"  温度: {TEMPERATURE}")
        print(f"  最大tokens: {MAX_TOKENS}")
        print(f"  GPU内存使用率: {GPU_MEMORY_UTILIZATION}")
        print(f"  张量并行大小: {TENSOR_PARALLEL_SIZE}")
    except Exception as e:
        print(f"✗ 配置文件导入失败: {e}")
        success = False

    try:
        print("检查工具函数...")
        from utils import safe_parse_int, read_csv, store_data
        print("✓ 工具函数导入成功")
    except Exception as e:
        print(f"✗ 工具函数导入失败: {e}")
        success = False

    if success:
        print("\n🎉 所有导入检查通过!")
        print("可以尝试运行: python test_offline_model.py")
    else:
        print("\n❌ 部分检查失败")
        print("请先解决上述问题")

    return success

if __name__ == "__main__":
    check_imports()
