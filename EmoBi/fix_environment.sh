#!/bin/bash

echo "=== 修复EmoBi环境依赖 ==="
echo "这个脚本将重新安装兼容的依赖版本"
echo

# 检查是否在conda环境中
if [[ "$CONDA_DEFAULT_ENV" != "EmoBi" ]]; then
    echo "警告: 请确保您在EmoBi conda环境中运行此脚本"
    echo "运行: conda activate EmoBi"
    exit 1
fi

echo "当前环境: $CONDA_DEFAULT_ENV"
echo

# 卸载可能冲突的包
echo "1. 卸载可能冲突的包..."
pip uninstall -y numpy scikit-learn torch torchaudio torchvision transformers vllm

echo "2. 清理pip缓存..."
pip cache purge

echo "3. 重新安装兼容的依赖..."

# 先安装numpy
echo "安装numpy..."
pip install "numpy>=1.24.0,<2.0.0"

# 安装scikit-learn
echo "安装scikit-learn..."
pip install "scikit-learn>=1.3.0"

# 安装PyTorch相关
echo "安装PyTorch..."
pip install torch>=2.0.1 torchaudio>=2.0.2 torchvision>=0.15.2

# 安装transformers
echo "安装transformers..."
pip install "transformers>=4.33.2"

# 安装其他依赖
echo "安装其他依赖..."
pip install "tqdm>=4.64.1" "pandas>=1.5.0"

# 最后安装vLLM
echo "安装vLLM..."
pip install "vllm>=0.2.0"

echo
echo "4. 验证安装..."
python -c "import numpy; print(f'numpy版本: {numpy.__version__}')"
python -c "import sklearn; print(f'scikit-learn版本: {sklearn.__version__}')"
python -c "import torch; print(f'torch版本: {torch.__version__}')"
python -c "import transformers; print(f'transformers版本: {transformers.__version__}')"

echo
echo "5. 测试vLLM导入..."
python -c "from vllm import LLM, SamplingParams; print('✓ vLLM导入成功')" 2>/dev/null && echo "vLLM导入测试通过" || echo "vLLM导入测试失败"

echo
echo "=== 环境修复完成 ==="
echo "现在可以尝试运行: python check_imports.py"
echo "如果成功，再运行: python main.py"
