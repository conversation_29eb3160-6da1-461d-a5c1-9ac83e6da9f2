# coding: UTF-8
import torch
from tqdm import tqdm
import time
from datetime import timedelta

PAD, CLS = '[PAD]', '[CLS]' 


def build_dataset(config, ues_word):

    def load_dataset(path, pad_size=32):
        contents = []
        line_count = 0
        with open(path, 'r', encoding='UTF-8') as f:
            for line in tqdm(f):
                line_count += 1
                lin = line.strip()
                if not lin:
                    continue
                
                # 添加错误处理
                try:
                    parts = lin.split('\t')
                    if len(parts) < 2:
                        print(f"警告: 第{line_count}行格式不正确，跳过: {lin[:100]}...")
                        continue
                    label, content = parts[0], parts[1]
                    if label == 'Label_id':
                        continue
                    content = content[:pad_size-2]
                    token = config.tokenizer.tokenize(content)
                    token = [CLS] + token
                    seq_len = len(token)
                    mask = []
                    token_ids = config.tokenizer.convert_tokens_to_ids(token)

                    if pad_size:
                        if len(token) < pad_size:
                            mask = [1] * len(token_ids) + [0] * (pad_size - len(token))
                            token_ids += ([0] * (pad_size - len(token)))
                        else:
                            mask = [1] * pad_size
                            token_ids = token_ids[:pad_size]
                            seq_len = pad_size
                    contents.append((token_ids, int(label), seq_len, mask))
                except Exception as e:
                    print(f"错误: 处理第{line_count}行时出错: {e}")
                    print(f"行内容: {lin[:200]}...")
                    continue
        return contents
    print("开始加载训练集...")
    train = load_dataset(config.train_path, config.pad_size)
    print(f"训练集加载完成，大小: {len(train)}")
    
    print("开始加载验证集...")
    dev = load_dataset(config.dev_path, config.pad_size)
    print(f"验证集加载完成，大小: {len(dev)}")
    
    print("开始加载测试集...")
    test = load_dataset(config.test_path, config.pad_size)
    print(f"测试集加载完成，大小: {len(test)}")
    
    print("=== build_dataset 完成 ===")
    vocab = list()
    return vocab, train, dev, test


class DatasetIterater(object):
    def __init__(self, batches, batch_size, device):
        self.batch_size = batch_size
        self.batches = batches
        self.n_batches = len(batches) // batch_size
        self.residue = False 
        if len(batches) % self.n_batches != 0:
            self.residue = True
        self.index = 0
        self.device = device

    def _to_tensor(self, datas):
        x = torch.LongTensor([_[0] for _ in datas]).to(self.device)
        y = torch.LongTensor([_[1] for _ in datas]).to(self.device)

        seq_len = torch.LongTensor([_[2] for _ in datas]).to(self.device)
        mask = torch.LongTensor([_[3] for _ in datas]).to(self.device)
        return (x, seq_len, mask), y

    def __next__(self):
        if self.residue and self.index == self.n_batches:
            batches = self.batches[self.index * self.batch_size: len(self.batches)]
            self.index += 1
            batches = self._to_tensor(batches)
            return batches

        elif self.index >= self.n_batches:
            self.index = 0
            raise StopIteration
        else:
            batches = self.batches[self.index * self.batch_size: (self.index + 1) * self.batch_size]
            self.index += 1
            batches = self._to_tensor(batches)
            return batches

    def __iter__(self):
        return self

    def __len__(self):
        if self.residue:
            return self.n_batches + 1
        else:
            return self.n_batches


def build_iterator(dataset, config):
    iter = DatasetIterater(dataset, config.batch_size, config.device)
    return iter


def get_time_dif(start_time):
    """获取已使用时间"""
    end_time = time.time()
    time_dif = end_time - start_time
    return timedelta(seconds=int(round(time_dif)))
