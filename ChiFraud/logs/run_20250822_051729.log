========================================
运行时间: Fri Aug 22 05:17:29 UTC 2025
========================================
Loading data...

0it [00:00, ?it/s]
561it [00:00, 5606.10it/s]
1122it [00:00, 5560.04it/s]
1709it [00:00, 5699.37it/s]
2304it [00:00, 5793.78it/s]
2884it [00:00, 5722.79it/s]
3457it [00:00, 5663.15it/s]
4040it [00:00, 5716.69it/s]
4614it [00:00, 5722.78it/s]
5216it [00:00, 5814.34it/s]
5798it [00:01, 5766.88it/s]
6421it [00:01, 5906.57it/s]
7012it [00:01, 5904.93it/s]
7603it [00:01, 5871.83it/s]
8199it [00:01, 5896.82it/s]
8789it [00:01, 5866.85it/s]
9376it [00:01, 5733.93it/s]
9951it [00:01, 5730.64it/s]
10538it [00:01, 5771.69it/s]
11116it [00:01, 5707.36it/s]
11688it [00:02, 5683.81it/s]
12262it [00:02, 5699.28it/s]
12894it [00:02, 5881.31it/s]
13483it [00:02, 5849.07it/s]
14069it [00:02, 5802.09it/s]
14692it [00:02, 5924.07it/s]
15285it [00:02, 5924.59it/s]
15878it [00:02, 5913.58it/s]
16500it [00:02, 5999.42it/s]
17101it [00:03, 4665.57it/s]
17721it [00:03, 5047.40it/s]
18315it [00:03, 5277.30it/s]
18905it [00:03, 5446.59it/s]
19510it [00:03, 5615.30it/s]
20090it [00:03, 5593.83it/s]
20701it [00:03, 5741.49it/s]
21311it [00:03, 5842.99it/s]
21914it [00:03, 5897.73it/s]
22509it [00:03, 5824.04it/s]
23095it [00:04, 5809.38it/s]
23679it [00:04, 5781.65it/s]
24259it [00:04, 5767.38it/s]
24837it [00:04, 5744.34it/s]
25413it [00:04, 5742.41it/s]
26007it [00:04, 5796.98it/s]
26591it [00:04, 5806.57it/s]
27181it [00:04, 5831.75it/s]
27778it [00:04, 5872.59it/s]
28366it [00:04, 5838.66it/s]
28951it [00:05, 5813.79it/s]
29554it [00:05, 5877.45it/s]
30160it [00:05, 5931.82it/s]
30754it [00:05, 5831.64it/s]
31360it [00:05, 5895.51it/s]
31950it [00:05, 5834.61it/s]
32534it [00:05, 5830.96it/s]
33123it [00:05, 5848.09it/s]
33709it [00:05, 5823.56it/s]
34292it [00:05, 5788.72it/s]
34871it [00:06, 5654.00it/s]
35473it [00:06, 5760.70it/s]
36050it [00:06, 5730.70it/s]
36624it [00:06, 5675.67it/s]
37212it [00:06, 5729.62it/s]
37794it [00:06, 5754.30it/s]
38370it [00:06, 5680.90it/s]
38939it [00:06, 5672.22it/s]
39507it [00:06, 5673.80it/s]
40101it [00:06, 5751.54it/s]
40704it [00:07, 5833.87it/s]
41288it [00:07, 5800.15it/s]
41869it [00:07, 5729.51it/s]
42485it [00:07, 5856.31it/s]
43071it [00:07, 5848.12it/s]
43665it [00:07, 5875.11it/s]
44253it [00:07, 5875.67it/s]
44841it [00:07, 5798.38it/s]
45422it [00:07, 5763.69it/s]
45999it [00:08, 5724.75it/s]
46572it [00:08, 5690.11it/s]
47152it [00:08, 5722.08it/s]
47725it [00:08, 3891.82it/s]
48317it [00:08, 4345.43it/s]
48914it [00:08, 4738.59it/s]
49491it [00:08, 5002.51it/s]
50092it [00:08, 5272.10it/s]
50664it [00:08, 5395.43it/s]
51284it [00:09, 5621.48it/s]
51866it [00:09, 5618.07it/s]
52456it [00:09, 5698.91it/s]
53068it [00:09, 5819.23it/s]
53657it [00:09, 5743.81it/s]
54237it [00:09, 5729.04it/s]
54814it [00:09, 5704.79it/s]
55419it [00:09, 5804.23it/s]
56002it [00:09, 5757.24it/s]
56607it [00:09, 5842.72it/s]
57193it [00:10, 5724.47it/s]
57767it [00:10, 5675.41it/s]
58350it [00:10, 5719.91it/s]
58923it [00:10, 5692.65it/s]
59498it [00:10, 5706.01it/s]
60071it [00:10, 5708.20it/s]
60663it [00:10, 5769.97it/s]
61272it [00:10, 5863.72it/s]
61859it [00:10, 5765.76it/s]
62463it [00:10, 5845.68it/s]
63067it [00:11, 5898.81it/s]
63682it [00:11, 5971.95it/s]
64283it [00:11, 5982.00it/s]
64882it [00:11, 5944.24it/s]
65477it [00:11, 5869.56it/s]
66065it [00:11, 5809.72it/s]
66647it [00:11, 5751.70it/s]
67226it [00:11, 5757.58it/s]
67802it [00:11, 5741.28it/s]
68390it [00:12, 5779.86it/s]
68969it [00:12, 5617.79it/s]
69554it [00:12, 5684.60it/s]
70124it [00:12, 5606.58it/s]
70701it [00:12, 5652.85it/s]
71267it [00:12, 5574.12it/s]
71867it [00:12, 5696.73it/s]
72438it [00:12, 5684.52it/s]
73026it [00:12, 5742.17it/s]
73629it [00:12, 5826.69it/s]
74217it [00:13, 5840.63it/s]
74802it [00:13, 5745.84it/s]
75399it [00:13, 5811.95it/s]
75981it [00:13, 5749.39it/s]
76562it [00:13, 5763.09it/s]
77156it [00:13, 5812.47it/s]
77738it [00:13, 5804.38it/s]
78319it [00:13, 5803.56it/s]
78900it [00:14, 3472.70it/s]
79443it [00:14, 3869.48it/s]
80044it [00:14, 4350.30it/s]
80617it [00:14, 4683.68it/s]
81204it [00:14, 4988.30it/s]
81764it [00:14, 5149.89it/s]
82394it [00:14, 5466.18it/s]
83006it [00:14, 5649.59it/s]
83593it [00:14, 5687.84it/s]
84202it [00:14, 5803.33it/s]
84799it [00:15, 5852.06it/s]
85392it [00:15, 5796.81it/s]
85978it [00:15, 5780.74it/s]
86560it [00:15, 5772.79it/s]
87140it [00:15, 5723.08it/s]
87715it [00:15, 5667.14it/s]
88284it [00:15, 5638.90it/s]
88866it [00:15, 5689.50it/s]
89476it [00:15, 5809.98it/s]
90058it [00:15, 5801.71it/s]
90639it [00:16, 5758.17it/s]
91216it [00:16, 5659.33it/s]
91837it [00:16, 5818.43it/s]
92420it [00:16, 5733.54it/s]
93014it [00:16, 5792.90it/s]
93594it [00:16, 5655.20it/s]
94161it [00:16, 5651.25it/s]
94743it [00:16, 5700.81it/s]
95314it [00:16, 5657.01it/s]
95924it [00:17, 5786.98it/s]
96528it [00:17, 5858.45it/s]
97141it [00:17, 5937.83it/s]
97736it [00:17, 5888.70it/s]
98326it [00:17, 5848.69it/s]
98928it [00:17, 5898.88it/s]
99520it [00:17, 5903.69it/s]
100111it [00:17, 5775.28it/s]
100697it [00:17, 5752.85it/s]
101284it [00:17, 5785.14it/s]
101877it [00:18, 5826.81it/s]
102461it [00:18, 5710.43it/s]
103033it [00:18, 5592.25it/s]
103601it [00:18, 5617.60it/s]
104192it [00:18, 5703.07it/s]
104793it [00:18, 5791.37it/s]
105377it [00:18, 5804.35it/s]
105958it [00:18, 5753.90it/s]
106534it [00:18, 5738.86it/s]
107109it [00:18, 5632.97it/s]
107673it [00:19, 5621.59it/s]
108295it [00:19, 5798.10it/s]
108877it [00:19, 5802.33it/s]
109478it [00:19, 5864.09it/s]
110065it [00:19, 5753.53it/s]
110656it [00:19, 5798.83it/s]
111237it [00:19, 5791.41it/s]
111836it [00:19, 5849.49it/s]
112422it [00:19, 5843.34it/s]
113007it [00:19, 5795.32it/s]
113587it [00:20, 5761.01it/s]
114178it [00:20, 5804.23it/s]
114759it [00:20, 5609.48it/s]
115354it [00:20, 5705.15it/s]
115947it [00:20, 5768.47it/s]
116525it [00:20, 5746.00it/s]
117101it [00:20, 5632.80it/s]
117666it [00:21, 2926.56it/s]
118255it [00:21, 3454.67it/s]
118851it [00:21, 3962.26it/s]
119411it [00:21, 4329.53it/s]
120019it [00:21, 4753.62it/s]
120573it [00:21, 4952.58it/s]
121147it [00:21, 5161.64it/s]
121735it [00:21, 5359.15it/s]
122314it [00:21, 5481.12it/s]
122896it [00:22, 5577.55it/s]
123470it [00:22, 5586.55it/s]
124065it [00:22, 5692.44it/s]
124643it [00:22, 5646.25it/s]
125255it [00:22, 5783.20it/s]
125838it [00:22, 5795.63it/s]
126421it [00:22, 5767.67it/s]
127006it [00:22, 5785.41it/s]
127602it [00:22, 5835.60it/s]
128187it [00:22, 5814.16it/s]
128770it [00:23, 5765.02it/s]
129362it [00:23, 5805.54it/s]
129949it [00:23, 5823.22it/s]
130532it [00:23, 5818.91it/s]
131131it [00:23, 5868.05it/s]
131718it [00:23, 5719.51it/s]
132338it [00:23, 5860.13it/s]
132951it [00:23, 5938.35it/s]
133546it [00:23, 5886.73it/s]
134136it [00:23, 5860.12it/s]
134723it [00:24, 5786.24it/s]
135303it [00:24, 5779.67it/s]
135882it [00:24, 5734.50it/s]
136471it [00:24, 5778.75it/s]
137084it [00:24, 5879.28it/s]
137673it [00:24, 5859.96it/s]
138261it [00:24, 5864.77it/s]
138848it [00:24, 5812.35it/s]
139452it [00:24, 5878.08it/s]
140040it [00:24, 5862.36it/s]
140639it [00:25, 5899.36it/s]
141230it [00:25, 5785.95it/s]
141854it [00:25, 5919.55it/s]
142468it [00:25, 5983.58it/s]
143067it [00:25, 5952.37it/s]
143663it [00:25, 5834.51it/s]
144248it [00:25, 5697.26it/s]
144819it [00:25, 5651.37it/s]
145385it [00:25, 5602.90it/s]
145946it [00:25, 5569.05it/s]
146552it [00:26, 5712.55it/s]
147131it [00:26, 5733.69it/s]
147711it [00:26, 5753.31it/s]
148287it [00:26, 5734.27it/s]
148870it [00:26, 5757.69it/s]
149446it [00:26, 5721.42it/s]
150019it [00:26, 5618.55it/s]
150612it [00:26, 5706.83it/s]
151197it [00:26, 5746.68it/s]
151788it [00:26, 5793.29it/s]
152370it [00:27, 5799.64it/s]
152984it [00:27, 5900.21it/s]
153600it [00:27, 5975.09it/s]
154198it [00:27, 5906.79it/s]
154792it [00:27, 5915.96it/s]
155384it [00:27, 5875.65it/s]
156001it [00:27, 5959.90it/s]
156598it [00:27, 5835.02it/s]
157193it [00:27, 5867.36it/s]
157795it [00:28, 5911.75it/s]
158387it [00:28, 5804.27it/s]
158982it [00:28, 5846.41it/s]
159568it [00:28, 5625.61it/s]
160158it [00:28, 5703.63it/s]
160731it [00:28, 5672.40it/s]
161306it [00:28, 5694.54it/s]
161889it [00:28, 5733.86it/s]
162464it [00:28, 5667.94it/s]
163032it [00:28, 5650.82it/s]
163647it [00:29, 5798.34it/s]
164256it [00:29, 5884.67it/s]
164845it [00:29, 5868.37it/s]
165433it [00:29, 5762.50it/s]
166029it [00:29, 5820.31it/s]
166662it [00:29, 5970.49it/s]
167297it [00:29, 6080.75it/s]
167906it [00:29, 5937.87it/s]
168501it [00:30, 2634.96it/s]
169087it [00:30, 3139.84it/s]
169680it [00:30, 3650.35it/s]
170255it [00:30, 4085.20it/s]
170809it [00:30, 4415.78it/s]
171404it [00:30, 4792.48it/s]
171984it [00:30, 5053.99it/s]
172558it [00:30, 5234.94it/s]
173179it [00:31, 5505.97it/s]
173762it [00:31, 5591.34it/s]
174353it [00:31, 5680.04it/s]
174951it [00:31, 5766.13it/s]
175565it [00:31, 5874.77it/s]
176161it [00:31, 5826.32it/s]
176750it [00:31, 5710.94it/s]
177358it [00:31, 5817.54it/s]
177976it [00:31, 5922.50it/s]
178574it [00:31, 5939.20it/s]
179180it [00:32, 5973.80it/s]
179788it [00:32, 6005.06it/s]
180394it [00:32, 6017.53it/s]
180997it [00:32, 5763.25it/s]
181577it [00:32, 5641.49it/s]
182144it [00:32, 5591.76it/s]
182728it [00:32, 5662.87it/s]
183317it [00:32, 5729.13it/s]
183891it [00:32, 5663.09it/s]
184459it [00:33, 5632.37it/s]
185046it [00:33, 5699.84it/s]
185623it [00:33, 5718.79it/s]
186215it [00:33, 5774.06it/s]
186793it [00:33, 5771.51it/s]
187382it [00:33, 5765.75it/s]
187996it [00:33, 5872.79it/s]
188584it [00:33, 5840.92it/s]
189169it [00:33, 5803.24it/s]
189750it [00:33, 5552.39it/s]
190313it [00:34, 5574.57it/s]
190897it [00:34, 5651.07it/s]
191481it [00:34, 5702.35it/s]
192055it [00:34, 5710.73it/s]
192627it [00:34, 5607.75it/s]
193189it [00:34, 5590.14it/s]
193568it [00:34, 5589.75it/s]

0it [00:00, ?it/s]
555it [00:00, 5548.75it/s]
1154it [00:00, 5807.94it/s]
1735it [00:00, 5779.59it/s]
2313it [00:00, 5598.78it/s]
2915it [00:00, 5744.98it/s]
3506it [00:00, 5795.61it/s]
4118it [00:00, 5900.02it/s]
4712it [00:00, 5910.97it/s]
5304it [00:00, 5786.91it/s]
5884it [00:01, 5644.53it/s]
6457it [00:01, 5668.23it/s]
7050it [00:01, 5746.05it/s]
7655it [00:01, 5836.34it/s]
8242it [00:01, 5845.11it/s]
8840it [00:01, 5883.78it/s]
9472it [00:01, 6012.96it/s]
10074it [00:01, 5992.37it/s]
10674it [00:01, 5841.11it/s]
11260it [00:01, 5834.88it/s]
11845it [00:02, 5737.77it/s]
12457it [00:02, 5848.29it/s]
13061it [00:02, 5904.48it/s]
13653it [00:02, 5903.79it/s]
14244it [00:02, 5881.39it/s]
14862it [00:02, 5968.88it/s]
15460it [00:02, 5946.46it/s]
16055it [00:02, 5925.30it/s]
16648it [00:02, 5907.04it/s]
17266it [00:02, 5985.29it/s]
17865it [00:03, 5944.26it/s]
18460it [00:03, 5909.71it/s]
19052it [00:03, 5820.77it/s]
19635it [00:03, 5795.11it/s]
20215it [00:03, 5790.86it/s]
20795it [00:03, 5771.37it/s]
21387it [00:03, 5813.37it/s]
21969it [00:03, 5792.07it/s]
22549it [00:03, 5680.78it/s]
23120it [00:03, 5688.50it/s]
23690it [00:04, 5680.11it/s]
24280it [00:04, 5743.34it/s]
24855it [00:04, 5703.20it/s]
25440it [00:04, 5741.37it/s]
26015it [00:04, 5740.80it/s]
26607it [00:04, 5790.33it/s]
27207it [00:04, 5850.06it/s]
27797it [00:04, 5863.43it/s]
28386it [00:04, 5869.90it/s]
28974it [00:04, 5826.51it/s]
29557it [00:05, 5781.93it/s]
30138it [00:05, 5788.78it/s]
30717it [00:05, 5711.33it/s]
31313it [00:05, 5784.43it/s]
31899it [00:05, 5806.58it/s]
32480it [00:05, 5726.54it/s]
33054it [00:05, 5590.59it/s]
33650it [00:05, 5693.80it/s]
34221it [00:05, 5596.38it/s]
34782it [00:06, 5520.54it/s]
35360it [00:06, 5595.23it/s]
35921it [00:06, 2104.48it/s]
36551it [00:06, 2676.12it/s]
37133it [00:06, 3188.42it/s]
37721it [00:07, 3696.42it/s]
38322it [00:07, 4187.03it/s]
38890it [00:07, 4532.96it/s]
39486it [00:07, 4888.78it/s]
40073it [00:07, 5144.27it/s]
40666it [00:07, 5358.55it/s]
41247it [00:07, 5482.86it/s]
41837it [00:07, 5601.41it/s]
42420it [00:07, 5636.19it/s]
43000it [00:07, 5594.92it/s]
43604it [00:08, 5723.33it/s]
44185it [00:08, 5686.58it/s]
44800it [00:08, 5814.51it/s]
45408it [00:08, 5892.22it/s]
46001it [00:08, 5886.46it/s]
46609it [00:08, 5942.45it/s]
47205it [00:08, 5840.49it/s]
47791it [00:08, 5845.85it/s]
48377it [00:08, 5809.08it/s]
48978it [00:08, 5866.32it/s]
49566it [00:09, 5783.76it/s]
50145it [00:09, 5732.62it/s]
50719it [00:09, 5710.24it/s]
51332it [00:09, 5831.77it/s]
51916it [00:09, 5799.33it/s]
52497it [00:09, 5734.82it/s]
53083it [00:09, 5771.70it/s]
53675it [00:09, 5811.50it/s]
54257it [00:09, 5754.99it/s]
54833it [00:10, 5737.95it/s]
55407it [00:10, 5643.11it/s]
56007it [00:10, 5747.02it/s]
56601it [00:10, 5803.42it/s]
57192it [00:10, 5832.82it/s]
57781it [00:10, 5849.08it/s]
58367it [00:10, 5796.71it/s]
58951it [00:10, 5806.96it/s]
59546it [00:10, 5848.54it/s]
60132it [00:10, 5735.29it/s]
60707it [00:11, 5679.62it/s]
61279it [00:11, 5689.91it/s]
61866it [00:11, 5742.38it/s]
62460it [00:11, 5799.70it/s]
63046it [00:11, 5816.31it/s]
63628it [00:11, 5771.14it/s]
64206it [00:11, 5758.21it/s]
64782it [00:11, 5720.39it/s]
65390it [00:11, 5824.05it/s]
65973it [00:11, 5786.65it/s]
66552it [00:12, 5488.06it/s]
67156it [00:12, 5645.85it/s]
67774it [00:12, 5798.23it/s]
68373it [00:12, 5852.72it/s]
68975it [00:12, 5896.64it/s]
69567it [00:12, 5835.45it/s]
70152it [00:12, 5835.71it/s]
70737it [00:12, 5788.76it/s]
71329it [00:12, 5826.03it/s]
71919it [00:12, 5846.26it/s]
72504it [00:13, 5796.00it/s]
73115it [00:13, 5888.06it/s]
73719it [00:13, 5932.65it/s]
74313it [00:13, 5867.60it/s]
74901it [00:13, 5843.37it/s]
75499it [00:13, 5883.31it/s]
76101it [00:13, 5922.63it/s]
76694it [00:13, 5822.13it/s]
77277it [00:13, 5779.92it/s]
77856it [00:13, 5659.30it/s]
78433it [00:14, 5684.30it/s]
79005it [00:14, 5693.34it/s]
79587it [00:14, 5729.12it/s]
80197it [00:14, 5829.43it/s]
80781it [00:14, 5828.24it/s]
81383it [00:14, 5881.35it/s]
82010it [00:14, 5996.29it/s]
82610it [00:14, 5958.18it/s]
83206it [00:14, 5940.41it/s]
83801it [00:15, 5892.31it/s]
84391it [00:15, 5871.24it/s]
84988it [00:15, 5898.85it/s]
85578it [00:15, 5764.55it/s]
86161it [00:15, 5782.01it/s]
86746it [00:15, 5798.88it/s]
87327it [00:15, 5785.18it/s]
87918it [00:15, 5822.17it/s]
88522it [00:15, 5883.59it/s]
89111it [00:15, 5794.54it/s]
89691it [00:16, 5679.29it/s]
90289it [00:16, 5765.70it/s]
90875it [00:16, 5792.90it/s]
91455it [00:16, 5754.81it/s]
92059it [00:16, 5836.09it/s]
92661it [00:16, 5884.67it/s]
93268it [00:16, 5938.21it/s]
93863it [00:16, 5925.28it/s]
94456it [00:16, 5910.99it/s]
95058it [00:16, 5941.58it/s]
95653it [00:17, 5778.21it/s]
96252it [00:17, 5839.78it/s]
96767it [00:17, 5615.44it/s]

0it [00:00, ?it/s]
591it [00:00, 5909.17it/s]
1222it [00:00, 6144.01it/s]
1838it [00:00, 6148.79it/s]
2453it [00:00, 6133.40it/s]
3074it [00:00, 6156.69it/s]
3718it [00:00, 6252.68it/s]
4344it [00:00, 6213.53it/s]
4966it [00:00, 6172.25it/s]
5584it [00:00, 6134.53it/s]
6198it [00:01, 6067.98it/s]
6809it [00:01, 6079.96it/s]
7431it [00:01, 6118.76it/s]
8043it [00:01, 6112.35it/s]
8660it [00:01, 6126.49it/s]
9273it [00:01, 6116.78it/s]
9901it [00:01, 6164.58it/s]
10518it [00:01, 6164.54it/s]
11169it [00:01, 6267.32it/s]
11796it [00:01, 6165.80it/s]
12456it [00:02, 6293.25it/s]
13093it [00:02, 6315.33it/s]
13725it [00:02, 6308.38it/s]
14357it [00:02, 6175.28it/s]
14989it [00:02, 6212.12it/s]
15627it [00:02, 6261.27it/s]
16273it [00:02, 6316.74it/s]
16906it [00:02, 6291.49it/s]
17536it [00:03, 1958.60it/s]
18158it [00:03, 2455.57it/s]
18719it [00:03, 2906.12it/s]
19343it [00:03, 3471.14it/s]
19941it [00:03, 3960.19it/s]
20566it [00:04, 4457.93it/s]
21220it [00:04, 4950.74it/s]
21859it [00:04, 5314.33it/s]
22475it [00:04, 5499.71it/s]
23086it [00:04, 5586.41it/s]
23729it [00:04, 5819.67it/s]
24355it [00:04, 5942.49it/s]
24973it [00:04, 5884.17it/s]
25578it [00:04, 5831.91it/s]
26216it [00:04, 5988.35it/s]
26858it [00:05, 6113.61it/s]
27476it [00:05, 6107.13it/s]
28120it [00:05, 6203.31it/s]
28744it [00:05, 6136.90it/s]
29361it [00:05, 6115.11it/s]
29975it [00:05, 6111.58it/s]
30588it [00:05, 6109.87it/s]
31200it [00:05, 6023.38it/s]
31804it [00:05, 5987.20it/s]
32404it [00:06, 5938.79it/s]
33021it [00:06, 6006.32it/s]
33657it [00:06, 6107.39it/s]
34269it [00:06, 6054.02it/s]
34920it [00:06, 6186.73it/s]
35561it [00:06, 6251.33it/s]
36187it [00:06, 6177.80it/s]
36818it [00:06, 6215.85it/s]
37453it [00:06, 6249.98it/s]
38079it [00:06, 6208.59it/s]
38701it [00:07, 6202.18it/s]
39329it [00:07, 6221.14it/s]
39952it [00:07, 6154.02it/s]
40590it [00:07, 6218.74it/s]
41226it [00:07, 6257.44it/s]
41854it [00:07, 6262.19it/s]
42481it [00:07, 6183.03it/s]
43127it [00:07, 6262.56it/s]
43754it [00:07, 6229.47it/s]
44388it [00:07, 6261.40it/s]
45015it [00:08, 6157.75it/s]
45632it [00:08, 6134.65it/s]
46253it [00:08, 6153.89it/s]
46869it [00:08, 6146.97it/s]
47506it [00:08, 6210.07it/s]
48128it [00:08, 6150.50it/s]
48756it [00:08, 6185.15it/s]
49375it [00:08, 5991.79it/s]
49989it [00:08, 6034.39it/s]
50594it [00:08, 5941.67it/s]
51220it [00:09, 6030.70it/s]
51839it [00:09, 6077.02it/s]
52477it [00:09, 6166.39it/s]
53104it [00:09, 6196.50it/s]
53725it [00:09, 6130.00it/s]
54353it [00:09, 6173.22it/s]
54971it [00:09, 6174.12it/s]
55589it [00:09, 6009.84it/s]
56192it [00:09, 5917.23it/s]
56813it [00:09, 6000.81it/s]
57430it [00:10, 6047.66it/s]
58036it [00:10, 6025.68it/s]
58640it [00:10, 6028.54it/s]
59244it [00:10, 5950.25it/s]
59868it [00:10, 6035.07it/s]
60472it [00:10, 6032.91it/s]
61085it [00:10, 6061.65it/s]
61692it [00:10, 5945.87it/s]
62316it [00:10, 6032.19it/s]
62935it [00:10, 6077.90it/s]
63544it [00:11, 6008.67it/s]
64160it [00:11, 6052.91it/s]
64766it [00:11, 5997.17it/s]
65382it [00:11, 6043.68it/s]
66010it [00:11, 6111.62it/s]
66641it [00:11, 6170.00it/s]
67259it [00:11, 6040.04it/s]
67873it [00:11, 6069.25it/s]
68497it [00:11, 6115.51it/s]
69109it [00:12, 6098.49it/s]
69720it [00:12, 6059.42it/s]
70327it [00:12, 6006.36it/s]
70949it [00:12, 6067.13it/s]
71557it [00:12, 6069.44it/s]
72170it [00:12, 6087.24it/s]
72779it [00:12, 6045.21it/s]
73433it [00:12, 6190.03it/s]
74053it [00:12, 6153.97it/s]
74672it [00:12, 6163.79it/s]
75289it [00:13, 6092.44it/s]
75899it [00:13, 6009.16it/s]
76503it [00:13, 6017.25it/s]
77125it [00:13, 6072.67it/s]
77769it [00:13, 6180.69it/s]
78388it [00:13, 6115.85it/s]
79034it [00:13, 6217.66it/s]
79657it [00:13, 6090.29it/s]
80267it [00:13, 6047.60it/s]
80875it [00:13, 6056.49it/s]
81482it [00:14, 5992.85it/s]
82090it [00:14, 6015.43it/s]
82726it [00:14, 6116.22it/s]
83338it [00:14, 6081.77it/s]
83947it [00:14, 6015.02it/s]
84550it [00:14, 6018.89it/s]
85153it [00:14, 6001.60it/s]
85754it [00:14, 5935.59it/s]
86348it [00:14, 5932.95it/s]
86942it [00:14, 5921.27it/s]
87535it [00:15, 5866.02it/s]
88122it [00:15, 5846.28it/s]
88759it [00:15, 5998.20it/s]
89375it [00:15, 6044.21it/s]
89980it [00:15, 6042.95it/s]
90604it [00:15, 6093.83it/s]
91214it [00:15, 6093.30it/s]
91840it [00:15, 6142.54it/s]
92455it [00:15, 6111.30it/s]
93087it [00:15, 6173.42it/s]
93705it [00:16, 6168.76it/s]
94323it [00:16, 6168.11it/s]
94940it [00:16, 6161.86it/s]
95557it [00:16, 6108.74it/s]
96168it [00:16, 6059.87it/s]
96780it [00:16, 6075.09it/s]
97409it [00:16, 6136.31it/s]
98023it [00:16, 6025.73it/s]
98641it [00:16, 6068.92it/s]
99254it [00:16, 6086.01it/s]
99863it [00:17, 6021.31it/s]
100492it [00:17, 6098.39it/s]
101111it [00:17, 6124.69it/s]
101738it [00:17, 6166.88it/s]
102372it [00:17, 6216.08it/s]
103011it [00:17, 6265.83it/s]
103638it [00:17, 6266.46it/s]
104302it [00:17, 6376.58it/s]
104940it [00:17, 6326.62it/s]
105573it [00:17, 6219.62it/s]
106196it [00:18, 6141.07it/s]
106822it [00:18, 6174.90it/s]
107443it [00:18, 6184.42it/s]
108076it [00:18, 6223.65it/s]
108711it [00:18, 6259.27it/s]
109338it [00:18, 6186.25it/s]
109957it [00:18, 6169.36it/s]
110575it [00:18, 6071.68it/s]
111188it [00:18, 6085.81it/s]
111797it [00:19, 6004.10it/s]
112398it [00:19, 5947.07it/s]
113043it [00:19, 6092.14it/s]
113694it [00:19, 6214.11it/s]
114323it [00:19, 6233.63it/s]
114947it [00:20, 1607.81it/s]
115592it [00:20, 2086.03it/s]
116175it [00:20, 2548.85it/s]
116821it [00:20, 3135.30it/s]
117441it [00:20, 3675.05it/s]
118023it [00:20, 4105.97it/s]
118605it [00:21, 4472.46it/s]
119201it [00:21, 4828.69it/s]
119809it [00:21, 5147.74it/s]
120427it [00:21, 5424.22it/s]
121041it [00:21, 5620.66it/s]
121102it [00:21, 5632.45it/s]
Time usage: 0:01:13
<bound method Module.parameters of Model(
  (bert): BertModel(
    (embeddings): BertEmbeddings(
      (word_embeddings): Embedding(30522, 768, padding_idx=0)
      (position_embeddings): Embedding(512, 768)
      (token_type_embeddings): Embedding(2, 768)
      (LayerNorm): BertLayerNorm()
      (dropout): Dropout(p=0.1, inplace=False)
    )
    (encoder): BertEncoder(
      (layer): ModuleList(
        (0-11): 12 x BertLayer(
          (attention): BertAttention(
            (self): BertSelfAttention(
              (query): Linear(in_features=768, out_features=768, bias=True)
              (key): Linear(in_features=768, out_features=768, bias=True)
              (value): Linear(in_features=768, out_features=768, bias=True)
              (dropout): Dropout(p=0.1, inplace=False)
            )
            (output): BertSelfOutput(
              (dense): Linear(in_features=768, out_features=768, bias=True)
              (LayerNorm): BertLayerNorm()
              (dropout): Dropout(p=0.1, inplace=False)
            )
          )
          (intermediate): BertIntermediate(
            (dense): Linear(in_features=768, out_features=3072, bias=True)
          )
          (output): BertOutput(
            (dense): Linear(in_features=3072, out_features=768, bias=True)
            (LayerNorm): BertLayerNorm()
            (dropout): Dropout(p=0.1, inplace=False)
          )
        )
      )
    )
    (pooler): BertPooler(
      (dense): Linear(in_features=768, out_features=768, bias=True)
      (activation): Tanh()
    )
  )
  (fc): Linear(in_features=768, out_features=11, bias=True)
)>
Epoch [1/50]
Traceback (most recent call last):
  File "/home/<USER>/JHLiu/ChiFraud/run.py", line 55, in <module>
    train(config, model, train_iter, dev_iter, test_iter)
  File "/home/<USER>/JHLiu/ChiFraud/train_eval.py", line 41, in train
    outputs = model(trains)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1773, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1784, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/JHLiu/ChiFraud/models/Bert.py", line 46, in forward
    _, pooled = self.bert(context, attention_mask=mask, output_all_encoded_layers=False)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1773, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1784, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/JHLiu/ChiFraud/pytorch_pretrained/modeling.py", line 731, in forward
    encoded_layers = self.encoder(embedding_output,
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1773, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1784, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/JHLiu/ChiFraud/pytorch_pretrained/modeling.py", line 406, in forward
    hidden_states = layer_module(hidden_states, attention_mask)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1773, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1784, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/JHLiu/ChiFraud/pytorch_pretrained/modeling.py", line 391, in forward
    attention_output = self.attention(hidden_states, attention_mask)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1773, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1784, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/JHLiu/ChiFraud/pytorch_pretrained/modeling.py", line 349, in forward
    self_output = self.self(input_tensor, attention_mask)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1773, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1784, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/JHLiu/ChiFraud/pytorch_pretrained/modeling.py", line 321, in forward
    context_layer = torch.matmul(attention_probs, value_layer)
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 96.00 MiB. GPU 0 has a total capacity of 31.37 GiB of which 23.50 MiB is free. Including non-PyTorch memory, this process has 31.34 GiB memory in use. Of the allocated memory 30.63 GiB is allocated by PyTorch, and 118.12 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
========================================
运行完成时间: Fri Aug 22 05:18:47 UTC 2025
