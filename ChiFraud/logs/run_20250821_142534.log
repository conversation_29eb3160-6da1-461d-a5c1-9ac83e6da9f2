========================================
运行时间: Thu Aug 21 14:25:34 UTC 2025
========================================
Loading data...

0it [00:00, ?it/s]
566it [00:00, 5657.23it/s]
1132it [00:00, 5657.21it/s]
1707it [00:00, 5693.25it/s]
2282it [00:00, 5713.52it/s]
2854it [00:00, 5625.89it/s]
3431it [00:00, 5672.78it/s]
4010it [00:00, 5708.87it/s]
4582it [00:00, 5679.29it/s]
5151it [00:00, 5652.60it/s]
5717it [00:01, 5465.74it/s]
6329it [00:01, 5658.14it/s]
6907it [00:01, 5690.66it/s]
7478it [00:01, 5582.53it/s]
8038it [00:01, 5583.34it/s]
8598it [00:01, 5578.25it/s]
9180it [00:01, 5647.81it/s]
9749it [00:01, 5658.54it/s]
10344it [00:01, 5741.68it/s]
10934it [00:01, 5788.26it/s]
11514it [00:02, 5713.00it/s]
12093it [00:02, 5734.26it/s]
12684it [00:02, 5785.06it/s]
13263it [00:02, 5720.54it/s]
13836it [00:02, 5558.74it/s]
14418it [00:02, 5629.04it/s]
15019it [00:02, 5740.11it/s]
15594it [00:02, 5700.84it/s]
16176it [00:02, 5733.46it/s]
16769it [00:02, 5787.55it/s]
17349it [00:03, 4552.26it/s]
17944it [00:03, 4898.77it/s]
18491it [00:03, 5048.52it/s]
19044it [00:03, 5179.18it/s]
19604it [00:03, 5295.37it/s]
20154it [00:03, 5351.39it/s]
20757it [00:03, 5546.40it/s]
21361it [00:03, 5684.86it/s]
21951it [00:03, 5746.25it/s]
22538it [00:04, 5781.10it/s]
23124it [00:04, 5804.18it/s]
23707it [00:04, 5667.90it/s]
24276it [00:04, 5629.91it/s]
24841it [00:04, 5614.08it/s]
25404it [00:04, 5597.39it/s]
25986it [00:04, 5659.58it/s]
26553it [00:04, 5653.30it/s]
27119it [00:04, 5654.98it/s]
27696it [00:04, 5685.23it/s]
28265it [00:05, 5643.95it/s]
28842it [00:05, 5681.16it/s]
29415it [00:05, 5694.00it/s]
29986it [00:05, 5698.00it/s]
30556it [00:05, 5623.00it/s]
31123it [00:05, 5635.62it/s]
31718it [00:05, 5728.45it/s]
32305it [00:05, 5770.21it/s]
32902it [00:05, 5828.17it/s]
33489it [00:05, 5838.87it/s]
34073it [00:06, 5715.26it/s]
34646it [00:06, 5690.04it/s]
35216it [00:06, 5653.87it/s]
35782it [00:06, 5603.78it/s]
36343it [00:06, 5521.55it/s]
36917it [00:06, 5585.35it/s]
37510it [00:06, 5685.27it/s]
38104it [00:06, 5759.92it/s]
38681it [00:06, 5655.26it/s]
39248it [00:06, 5571.28it/s]
39815it [00:07, 5600.09it/s]
40412it [00:07, 5707.62it/s]
40998it [00:07, 5748.82it/s]
41574it [00:07, 5556.33it/s]
42132it [00:07, 5386.66it/s]
42738it [00:07, 5579.26it/s]
43310it [00:07, 5619.69it/s]
43891it [00:07, 5674.69it/s]
44462it [00:07, 5683.73it/s]
45032it [00:08, 5629.35it/s]
45596it [00:08, 5617.02it/s]
46190it [00:08, 5712.23it/s]
46776it [00:08, 5753.59it/s]
47352it [00:08, 5655.87it/s]
47919it [00:08, 3825.22it/s]
48509it [00:08, 4284.41it/s]
49096it [00:08, 4663.79it/s]
49653it [00:08, 4894.84it/s]
50249it [00:09, 5176.98it/s]
50815it [00:09, 5309.97it/s]
51421it [00:09, 5517.47it/s]
51992it [00:09, 5505.69it/s]
52557it [00:09, 5452.02it/s]
53162it [00:09, 5623.62it/s]
53732it [00:09, 5538.89it/s]
54292it [00:09, 5555.62it/s]
54852it [00:09, 5542.00it/s]
55415it [00:10, 5565.40it/s]
55980it [00:10, 5521.79it/s]
56569it [00:10, 5629.07it/s]
57153it [00:10, 5685.91it/s]
57723it [00:10, 5660.89it/s]
58290it [00:10, 5655.26it/s]
58856it [00:10, 5591.11it/s]
59427it [00:10, 5625.77it/s]
60011it [00:10, 5686.87it/s]
60593it [00:10, 5724.91it/s]
61175it [00:11, 5752.94it/s]
61751it [00:11, 5712.11it/s]
62323it [00:11, 5708.48it/s]
62901it [00:11, 5729.50it/s]
63490it [00:11, 5776.02it/s]
64068it [00:11, 5710.16it/s]
64640it [00:11, 5676.73it/s]
65222it [00:11, 5717.50it/s]
65800it [00:11, 5734.55it/s]
66382it [00:11, 5759.44it/s]
66959it [00:12, 5754.66it/s]
67535it [00:12, 5724.83it/s]
68108it [00:12, 5718.11it/s]
68680it [00:12, 5628.57it/s]
69244it [00:12, 5512.88it/s]
69804it [00:12, 5532.94it/s]
70358it [00:12, 5460.87it/s]
70905it [00:12, 5347.68it/s]
71460it [00:12, 5405.62it/s]
72027it [00:12, 5480.56it/s]
72605it [00:13, 5568.35it/s]
73178it [00:13, 5613.25it/s]
73784it [00:13, 5744.07it/s]
74359it [00:13, 5728.22it/s]
74933it [00:13, 5439.62it/s]
75481it [00:13, 5339.72it/s]
76054it [00:13, 5449.89it/s]
76602it [00:13, 5430.46it/s]
77181it [00:13, 5535.08it/s]
77739it [00:13, 5548.19it/s]
78317it [00:14, 5616.72it/s]
78880it [00:14, 3357.96it/s]
79441it [00:14, 3813.66it/s]
80005it [00:14, 4222.87it/s]
80574it [00:14, 4578.34it/s]
81142it [00:14, 4861.80it/s]
81694it [00:14, 5037.58it/s]
82291it [00:15, 5290.66it/s]
82876it [00:15, 5448.06it/s]
83444it [00:15, 5512.23it/s]
84023it [00:15, 5591.73it/s]
84602it [00:15, 5649.52it/s]
85175it [00:15, 5635.86it/s]
85744it [00:15, 5514.92it/s]
86303it [00:15, 5532.36it/s]
86860it [00:15, 5447.67it/s]
87431it [00:15, 5520.04it/s]
88000it [00:16, 5566.59it/s]
88561it [00:16, 5579.04it/s]
89142it [00:16, 5645.06it/s]
89732it [00:16, 5719.27it/s]
90314it [00:16, 5747.31it/s]
90890it [00:16, 5724.96it/s]
91488it [00:16, 5800.01it/s]
92069it [00:16, 5798.71it/s]
92650it [00:16, 5716.72it/s]
93223it [00:16, 5694.45it/s]
93793it [00:17, 5689.59it/s]
94375it [00:17, 5724.92it/s]
94948it [00:17, 5703.16it/s]
95519it [00:17, 5663.21it/s]
96086it [00:17, 5645.87it/s]
96667it [00:17, 5693.17it/s]
97237it [00:17, 5634.68it/s]
97809it [00:17, 5658.52it/s]
98375it [00:17, 5631.29it/s]
98952it [00:17, 5669.96it/s]
99526it [00:18, 5689.39it/s]
100096it [00:18, 5612.53it/s]
100676it [00:18, 5667.35it/s]
101243it [00:18, 5641.56it/s]
101808it [00:18, 5547.44it/s]
102364it [00:18, 5476.81it/s]
102919it [00:18, 5497.59it/s]
103482it [00:18, 5534.59it/s]
104061it [00:18, 5606.01it/s]
104651it [00:18, 5686.54it/s]
105235it [00:19, 5731.64it/s]
105809it [00:19, 5732.30it/s]
106383it [00:19, 5637.02it/s]
106948it [00:19, 5626.76it/s]
107511it [00:19, 5564.61it/s]
108081it [00:19, 5599.03it/s]
108642it [00:19, 5521.28it/s]
109216it [00:19, 5584.75it/s]
109775it [00:19, 5573.38it/s]
110341it [00:19, 5598.68it/s]
110902it [00:20, 5586.50it/s]
111509it [00:20, 5726.80it/s]
112082it [00:20, 5647.97it/s]
112655it [00:20, 5671.64it/s]
113223it [00:20, 5578.57it/s]
113802it [00:20, 5638.27it/s]
114401it [00:20, 5740.63it/s]
114976it [00:20, 5645.54it/s]
115542it [00:20, 5623.82it/s]
116111it [00:21, 5638.76it/s]
116690it [00:21, 5683.02it/s]
117259it [00:21, 5593.93it/s]
117819it [00:21, 2858.76it/s]
118341it [00:21, 3276.49it/s]
118941it [00:21, 3826.17it/s]
119482it [00:21, 4179.39it/s]
120089it [00:22, 4637.17it/s]
120646it [00:22, 4876.21it/s]
121224it [00:22, 5117.11it/s]
121780it [00:22, 5234.67it/s]
122335it [00:22, 5229.69it/s]
122887it [00:22, 5310.72it/s]
123434it [00:22, 5352.69it/s]
124003it [00:22, 5450.56it/s]
124584it [00:22, 5553.34it/s]
125181it [00:22, 5675.64it/s]
125757it [00:23, 5700.22it/s]
126331it [00:23, 5665.36it/s]
126900it [00:23, 5665.96it/s]
127484it [00:23, 5713.83it/s]
128057it [00:23, 5557.50it/s]
128629it [00:23, 5604.91it/s]
129191it [00:23, 5535.39it/s]
129748it [00:23, 5544.64it/s]
130304it [00:23, 5475.94it/s]
130865it [00:23, 5514.20it/s]
131430it [00:24, 5553.53it/s]
132000it [00:24, 5594.81it/s]
132561it [00:24, 5597.21it/s]
133144it [00:24, 5665.07it/s]
133728it [00:24, 5716.64it/s]
134300it [00:24, 5673.38it/s]
134876it [00:24, 5696.94it/s]
135446it [00:24, 5689.84it/s]
136029it [00:24, 5729.41it/s]
136649it [00:24, 5866.99it/s]
137236it [00:25, 5846.87it/s]
137821it [00:25, 5805.57it/s]
138402it [00:25, 5720.77it/s]
138993it [00:25, 5775.50it/s]
139574it [00:25, 5783.63it/s]
140153it [00:25, 5745.30it/s]
140731it [00:25, 5754.85it/s]
141307it [00:25, 5594.25it/s]
141909it [00:25, 5715.89it/s]
142491it [00:25, 5741.54it/s]
143070it [00:26, 5755.28it/s]
143647it [00:26, 5722.11it/s]
144220it [00:26, 5677.43it/s]
144789it [00:26, 5593.60it/s]
145349it [00:26, 5521.47it/s]
145902it [00:26, 5460.05it/s]
146477it [00:26, 5543.91it/s]
147086it [00:26, 5698.36it/s]
147687it [00:26, 5788.89it/s]
148270it [00:27, 5798.01it/s]
148851it [00:27, 5778.09it/s]
149430it [00:27, 5779.27it/s]
150009it [00:27, 5736.71it/s]
150583it [00:27, 5636.46it/s]
151148it [00:27, 5551.58it/s]
151740it [00:27, 5657.92it/s]
152307it [00:27, 5614.44it/s]
152869it [00:27, 5612.70it/s]
153458it [00:27, 5694.76it/s]
154028it [00:28, 5631.22it/s]
154627it [00:28, 5735.55it/s]
155201it [00:28, 5710.95it/s]
155787it [00:28, 5753.64it/s]
156363it [00:28, 5678.53it/s]
156932it [00:28, 5561.43it/s]
157494it [00:28, 5577.76it/s]
158079it [00:28, 5655.11it/s]
158652it [00:28, 5676.85it/s]
159227it [00:28, 5694.41it/s]
159797it [00:29, 5634.21it/s]
160366it [00:29, 5650.32it/s]
160932it [00:29, 5648.94it/s]
161509it [00:29, 5682.20it/s]
162078it [00:29, 5677.84it/s]
162646it [00:29, 5601.02it/s]
163218it [00:29, 5634.78it/s]
163799it [00:29, 5686.42it/s]
164372it [00:29, 5697.21it/s]
164942it [00:29, 5546.44it/s]
165498it [00:30, 5520.84it/s]
166082it [00:30, 5610.32it/s]
166706it [00:30, 5793.38it/s]
167318it [00:30, 5887.00it/s]
167908it [00:30, 5743.03it/s]
168484it [00:31, 2524.45it/s]
169074it [00:31, 3048.54it/s]
169664it [00:31, 3563.82it/s]
170257it [00:31, 4050.76it/s]
170817it [00:31, 4402.68it/s]
171397it [00:31, 4743.47it/s]
171969it [00:31, 4996.50it/s]
172529it [00:31, 5152.33it/s]
173119it [00:31, 5359.81it/s]
173688it [00:31, 5300.38it/s]
174255it [00:32, 5404.88it/s]
174843it [00:32, 5539.20it/s]
175445it [00:32, 5672.95it/s]
176022it [00:32, 5651.28it/s]
176594it [00:32, 5481.86it/s]
177165it [00:32, 5546.86it/s]
177779it [00:32, 5717.56it/s]
178354it [00:32, 5697.73it/s]
178959it [00:32, 5794.23it/s]
179541it [00:32, 5748.91it/s]
180132it [00:33, 5795.22it/s]
180713it [00:33, 5716.60it/s]
181306it [00:33, 5776.48it/s]
181885it [00:33, 5628.70it/s]
182450it [00:33, 5522.70it/s]
183025it [00:33, 5585.07it/s]
183634it [00:33, 5731.37it/s]
184209it [00:33, 5704.96it/s]
184781it [00:33, 5640.53it/s]
185346it [00:33, 5530.63it/s]
185924it [00:34, 5603.00it/s]
186515it [00:34, 5689.63it/s]
187085it [00:34, 5612.92it/s]
187647it [00:34, 5571.82it/s]
188252it [00:34, 5712.37it/s]
188824it [00:34, 5676.98it/s]
189393it [00:34, 5636.17it/s]
189973it [00:34, 5677.91it/s]
190542it [00:34, 5674.31it/s]
191110it [00:34, 5646.80it/s]
191675it [00:35, 5647.07it/s]
192253it [00:35, 5684.37it/s]
192822it [00:35, 5646.28it/s]
193387it [00:35, 5555.80it/s]
193568it [00:35, 5462.21it/s]

0it [00:00, ?it/s]
570it [00:00, 5679.18it/s]
1150it [00:00, 5745.34it/s]
1725it [00:00, 5747.04it/s]
2300it [00:00, 5548.36it/s]
2858it [00:00, 5557.06it/s]
3424it [00:00, 5588.07it/s]
4029it [00:00, 5734.86it/s]
4618it [00:00, 5781.07it/s]
5197it [00:00, 5665.18it/s]
5771it [00:01, 5684.89it/s]
6340it [00:01, 5684.92it/s]
6919it [00:01, 5713.03it/s]
7491it [00:01, 5692.86it/s]
8087it [00:01, 5771.93it/s]
8670it [00:01, 5788.86it/s]
9263it [00:01, 5830.79it/s]
9868it [00:01, 5894.29it/s]
10458it [00:01, 5882.04it/s]
11047it [00:01, 5861.39it/s]
11634it [00:02, 5764.20it/s]
12238it [00:02, 5844.93it/s]
12826it [00:02, 5851.47it/s]
13412it [00:02, 5845.58it/s]
13997it [00:02, 5819.42it/s]
14580it [00:02, 5734.49it/s]
15154it [00:02, 5619.40it/s]
15751it [00:02, 5721.37it/s]
16324it [00:02, 5627.56it/s]
16921it [00:02, 5724.64it/s]
17495it [00:03, 5674.04it/s]
18063it [00:03, 5648.60it/s]
18629it [00:03, 5637.51it/s]
19193it [00:03, 5598.72it/s]
19754it [00:03, 5583.50it/s]
20329it [00:03, 5632.39it/s]
20893it [00:03, 5593.20it/s]
21493it [00:03, 5712.56it/s]
22065it [00:03, 5675.07it/s]
22633it [00:03, 5649.41it/s]
23207it [00:04, 5675.42it/s]
23778it [00:04, 5684.40it/s]
24347it [00:04, 5685.17it/s]
24916it [00:04, 5636.63it/s]
25493it [00:04, 5673.82it/s]
26061it [00:04, 5558.71it/s]
26636it [00:04, 5614.54it/s]
27225it [00:04, 5694.29it/s]
27808it [00:04, 5731.20it/s]
28390it [00:04, 5755.70it/s]
28966it [00:05, 5711.04it/s]
29538it [00:05, 5669.16it/s]
30109it [00:05, 5679.81it/s]
30678it [00:05, 5652.36it/s]
31252it [00:05, 5673.08it/s]
31820it [00:05, 5646.43it/s]
32385it [00:05, 5639.92it/s]
32950it [00:05, 5530.83it/s]
33524it [00:05, 5588.20it/s]
34084it [00:05, 5587.45it/s]
34644it [00:06, 5589.14it/s]
35218it [00:06, 5631.66it/s]
35782it [00:06, 5459.55it/s]
36330it [00:06, 2026.42it/s]
36882it [00:07, 2494.65it/s]
37466it [00:07, 3031.46it/s]
38028it [00:07, 3513.63it/s]
38601it [00:07, 3976.89it/s]
39174it [00:07, 4380.41it/s]
39758it [00:07, 4739.15it/s]
40358it [00:07, 5068.19it/s]
40926it [00:07, 5210.88it/s]
41492it [00:07, 5317.35it/s]
42062it [00:07, 5425.55it/s]
42628it [00:08, 5426.84it/s]
43208it [00:08, 5530.37it/s]
43809it [00:08, 5668.21it/s]
44385it [00:08, 5621.81it/s]
44969it [00:08, 5685.03it/s]
45542it [00:08, 5688.83it/s]
46114it [00:08, 5635.41it/s]
46680it [00:08, 5609.12it/s]
47243it [00:08, 5548.08it/s]
47820it [00:09, 5609.65it/s]
48389it [00:09, 5633.35it/s]
48969it [00:09, 5681.23it/s]
49538it [00:09, 5642.44it/s]
50103it [00:09, 5640.35it/s]
50672it [00:09, 5654.73it/s]
51258it [00:09, 5715.36it/s]
51831it [00:09, 5718.91it/s]
52404it [00:09, 5692.97it/s]
52974it [00:09, 5639.51it/s]
53544it [00:10, 5657.30it/s]
54113it [00:10, 5663.37it/s]
54685it [00:10, 5679.46it/s]
55254it [00:10, 5612.88it/s]
55829it [00:10, 5651.85it/s]
56422it [00:10, 5728.11it/s]
56995it [00:10, 5671.02it/s]
57563it [00:10, 5507.25it/s]
58135it [00:10, 5568.87it/s]
58693it [00:10, 5516.04it/s]
59259it [00:11, 5557.38it/s]
59816it [00:11, 5526.63it/s]
60384it [00:11, 5570.38it/s]
60946it [00:11, 5579.73it/s]
61530it [00:11, 5652.43it/s]
62100it [00:11, 5664.05it/s]
62667it [00:11, 5611.44it/s]
63229it [00:11, 5413.38it/s]
63772it [00:11, 5408.53it/s]
64314it [00:11, 5325.57it/s]
64848it [00:12, 5292.08it/s]
65443it [00:12, 5482.05it/s]
66032it [00:12, 5601.90it/s]
66593it [00:12, 5560.77it/s]
67186it [00:12, 5667.11it/s]
67778it [00:12, 5741.80it/s]
68353it [00:12, 5660.85it/s]
68920it [00:12, 5459.79it/s]
69468it [00:12, 5407.10it/s]
70010it [00:12, 5393.36it/s]
70551it [00:13, 5327.96it/s]
71143it [00:13, 5494.16it/s]
71699it [00:13, 5512.68it/s]
72253it [00:13, 5520.47it/s]
72846it [00:13, 5641.75it/s]
73432it [00:13, 5705.22it/s]
74008it [00:13, 5715.96it/s]
74580it [00:13, 5682.99it/s]
75162it [00:13, 5721.13it/s]
75739it [00:13, 5735.21it/s]
76313it [00:14, 5722.30it/s]
76886it [00:14, 5692.70it/s]
77457it [00:14, 5694.85it/s]
78027it [00:14, 5605.85it/s]
78605it [00:14, 5652.69it/s]
79171it [00:14, 5595.62it/s]
79731it [00:14, 5585.36it/s]
80290it [00:14, 5485.30it/s]
80887it [00:14, 5626.80it/s]
81484it [00:15, 5726.74it/s]
82098it [00:15, 5848.35it/s]
82684it [00:15, 5819.33it/s]
83267it [00:15, 5778.74it/s]
83846it [00:15, 5715.96it/s]
84418it [00:15, 5674.71it/s]
84986it [00:15, 5627.74it/s]
85549it [00:15, 5542.14it/s]
86111it [00:15, 5562.54it/s]
86672it [00:15, 5574.28it/s]
87248it [00:16, 5627.43it/s]
87818it [00:16, 5647.99it/s]
88411it [00:16, 5729.65it/s]
88985it [00:16, 5664.66it/s]
89561it [00:16, 5691.68it/s]
90143it [00:16, 5724.92it/s]
90723it [00:16, 5736.95it/s]
91297it [00:16, 5666.54it/s]
91864it [00:16, 5510.91it/s]
92417it [00:16, 5469.77it/s]
92965it [00:17, 5470.35it/s]
93553it [00:17, 5590.56it/s]
94113it [00:17, 5561.62it/s]
94687it [00:17, 5614.00it/s]
95266it [00:17, 5666.20it/s]
95845it [00:17, 5702.88it/s]
96431it [00:17, 5748.69it/s]
96767it [00:17, 5460.58it/s]

0it [00:00, ?it/s]
576it [00:00, 5756.94it/s]
1201it [00:00, 6046.57it/s]
1806it [00:00, 5855.78it/s]
2393it [00:00, 5828.29it/s]
3003it [00:00, 5923.51it/s]
3634it [00:00, 6050.38it/s]
4240it [00:00, 5942.42it/s]
4848it [00:00, 5983.45it/s]
5465it [00:00, 6038.78it/s]
6070it [00:01, 5946.50it/s]
6666it [00:01, 5798.79it/s]
7249it [00:01, 5807.64it/s]
7845it [00:01, 5852.70it/s]
8444it [00:01, 5891.68it/s]
9034it [00:01, 5890.40it/s]
9624it [00:01, 5882.00it/s]
10221it [00:01, 5904.86it/s]
10850it [00:01, 6018.21it/s]
11452it [00:01, 5901.12it/s]
12096it [00:02, 6059.84it/s]
12736it [00:02, 6159.05it/s]
13369it [00:02, 6208.99it/s]
13991it [00:02, 6170.55it/s]
14609it [00:02, 5998.96it/s]
15232it [00:02, 6058.85it/s]
15847it [00:02, 6084.40it/s]
16457it [00:02, 6077.27it/s]
17066it [00:02, 6059.48it/s]
17673it [00:03, 1873.34it/s]
18279it [00:03, 2358.33it/s]
18878it [00:03, 2873.62it/s]
19484it [00:04, 3410.34it/s]
20043it [00:04, 3831.53it/s]
20613it [00:04, 4236.05it/s]
21170it [00:04, 4547.74it/s]
21767it [00:04, 4906.05it/s]
22361it [00:04, 5178.87it/s]
22937it [00:04, 5320.84it/s]
23541it [00:04, 5519.17it/s]
24157it [00:04, 5700.69it/s]
24754it [00:04, 5778.41it/s]
25350it [00:05, 5830.45it/s]
25945it [00:05, 5862.29it/s]
26540it [00:05, 5802.23it/s]
27126it [00:05, 5766.28it/s]
27728it [00:05, 5839.63it/s]
28315it [00:05, 5847.17it/s]
28911it [00:05, 5880.45it/s]
29509it [00:05, 5909.00it/s]
30101it [00:05, 5904.61it/s]
30693it [00:05, 5873.32it/s]
31281it [00:06, 5767.42it/s]
31859it [00:06, 5767.59it/s]
32437it [00:06, 5724.08it/s]
33060it [00:06, 5872.67it/s]
33676it [00:06, 5956.92it/s]
34273it [00:06, 5889.07it/s]
34903it [00:06, 6006.90it/s]
35527it [00:06, 6074.21it/s]
36135it [00:06, 6038.65it/s]
36740it [00:06, 5938.24it/s]
37362it [00:07, 6019.43it/s]
37965it [00:07, 6019.49it/s]
38568it [00:07, 6003.84it/s]
39169it [00:07, 5999.02it/s]
39770it [00:07, 5895.23it/s]
40387it [00:07, 5975.34it/s]
41005it [00:07, 6035.86it/s]
41609it [00:07, 6029.32it/s]
42213it [00:07, 5892.44it/s]
42807it [00:07, 5905.52it/s]
43425it [00:08, 5985.75it/s]
44031it [00:08, 6004.26it/s]
44637it [00:08, 6019.86it/s]
45240it [00:08, 5837.67it/s]
45837it [00:08, 5875.36it/s]
46469it [00:08, 6005.50it/s]
47071it [00:08, 6000.91it/s]
47694it [00:08, 6068.54it/s]
48302it [00:08, 5941.43it/s]
48918it [00:08, 6003.73it/s]
49520it [00:09, 5957.30it/s]
50120it [00:09, 5964.42it/s]
50717it [00:09, 5707.71it/s]
51294it [00:09, 5724.92it/s]
51899it [00:09, 5818.66it/s]
52489it [00:09, 5841.26it/s]
53100it [00:09, 5920.06it/s]
53693it [00:09, 5893.83it/s]
54289it [00:09, 5913.44it/s]
54889it [00:09, 5937.66it/s]
55490it [00:10, 5959.00it/s]
56087it [00:10, 5879.59it/s]
56676it [00:10, 5686.16it/s]
57264it [00:10, 5739.18it/s]
57883it [00:10, 5871.21it/s]
58477it [00:10, 5886.75it/s]
59067it [00:10, 5749.33it/s]
59659it [00:10, 5799.05it/s]
60251it [00:10, 5832.71it/s]
60844it [00:11, 5860.56it/s]
61431it [00:11, 5844.49it/s]
62016it [00:11, 5701.61it/s]
62641it [00:11, 5861.69it/s]
63229it [00:11, 5847.66it/s]
63815it [00:11, 5806.74it/s]
64413it [00:11, 5854.31it/s]
64999it [00:11, 5794.75it/s]
65589it [00:11, 5825.20it/s]
66210it [00:11, 5938.59it/s]
66806it [00:12, 5942.95it/s]
67401it [00:12, 5829.66it/s]
67993it [00:12, 5853.23it/s]
68622it [00:12, 5980.61it/s]
69261it [00:12, 6101.12it/s]
69872it [00:12, 6006.36it/s]
70474it [00:12, 5940.69it/s]
71071it [00:12, 5948.97it/s]
71667it [00:12, 5916.30it/s]
72284it [00:12, 5989.59it/s]
72884it [00:13, 5953.12it/s]
73511it [00:13, 6044.51it/s]
74116it [00:13, 5966.00it/s]
74713it [00:13, 5847.82it/s]
75323it [00:13, 5919.29it/s]
75916it [00:13, 5846.37it/s]
76514it [00:13, 5884.11it/s]
77111it [00:13, 5907.48it/s]
77728it [00:13, 5985.16it/s]
78329it [00:13, 5991.78it/s]
78929it [00:14, 5978.97it/s]
79529it [00:14, 5984.29it/s]
80128it [00:14, 5901.83it/s]
80719it [00:14, 5870.71it/s]
81307it [00:14, 5798.18it/s]
81893it [00:14, 5812.12it/s]
82525it [00:14, 5961.42it/s]
83122it [00:14, 5864.65it/s]
83724it [00:14, 5909.54it/s]
84316it [00:14, 5820.18it/s]
84914it [00:15, 5862.63it/s]
85501it [00:15, 5849.58it/s]
86087it [00:15, 5697.72it/s]
86662it [00:15, 5711.87it/s]
87234it [00:15, 5650.20it/s]
87826it [00:15, 5723.87it/s]
88420it [00:15, 5786.69it/s]
89041it [00:15, 5911.66it/s]
89633it [00:15, 5815.51it/s]
90249it [00:16, 5914.09it/s]
90841it [00:16, 5911.66it/s]
91440it [00:16, 5932.14it/s]
92044it [00:16, 5963.54it/s]
92641it [00:16, 5948.63it/s]
93248it [00:16, 5983.95it/s]
93851it [00:16, 5996.12it/s]
94468it [00:16, 6043.56it/s]
95073it [00:16, 5993.12it/s]
95673it [00:16, 5933.83it/s]
96267it [00:17, 5903.01it/s]
96864it [00:17, 5916.51it/s]
97462it [00:17, 5935.23it/s]
98056it [00:17, 5815.66it/s]
98639it [00:17, 5765.69it/s]
99238it [00:17, 5829.76it/s]
99854it [00:17, 5927.22it/s]
100468it [00:17, 5989.58it/s]
101069it [00:17, 5990.79it/s]
101669it [00:17, 5919.76it/s]
102276it [00:18, 5961.20it/s]
102881it [00:18, 5986.84it/s]
103480it [00:18, 5944.86it/s]
104115it [00:18, 6064.06it/s]
104733it [00:18, 6093.90it/s]
105343it [00:18, 6039.38it/s]
105970it [00:18, 6103.94it/s]
106581it [00:18, 5961.65it/s]
107201it [00:18, 6029.31it/s]
107814it [00:18, 6058.88it/s]
108450it [00:19, 6147.43it/s]
109066it [00:19, 6030.58it/s]
109670it [00:19, 5872.72it/s]
110261it [00:19, 5880.45it/s]
110850it [00:19, 5793.85it/s]
111431it [00:19, 5717.93it/s]
112004it [00:19, 5682.82it/s]
112599it [00:19, 5758.71it/s]
113229it [00:19, 5917.96it/s]
113841it [00:19, 5974.19it/s]
114470it [00:20, 6060.91it/s]
115077it [00:21, 1551.42it/s]
115684it [00:21, 1994.94it/s]
116244it [00:21, 2438.26it/s]
116855it [00:21, 2985.60it/s]
117436it [00:21, 3481.53it/s]
117997it [00:21, 3905.89it/s]
118577it [00:21, 4326.45it/s]
119179it [00:21, 4734.24it/s]
119767it [00:21, 5027.20it/s]
120345it [00:22, 5217.36it/s]
120942it [00:22, 5424.98it/s]
121102it [00:22, 5456.04it/s]
Time usage: 0:01:15
<bound method Module.parameters of Model(
  (bert): BertModel(
    (embeddings): BertEmbeddings(
      (word_embeddings): Embedding(30522, 768, padding_idx=0)
      (position_embeddings): Embedding(512, 768)
      (token_type_embeddings): Embedding(2, 768)
      (LayerNorm): BertLayerNorm()
      (dropout): Dropout(p=0.1, inplace=False)
    )
    (encoder): BertEncoder(
      (layer): ModuleList(
        (0-11): 12 x BertLayer(
          (attention): BertAttention(
            (self): BertSelfAttention(
              (query): Linear(in_features=768, out_features=768, bias=True)
              (key): Linear(in_features=768, out_features=768, bias=True)
              (value): Linear(in_features=768, out_features=768, bias=True)
              (dropout): Dropout(p=0.1, inplace=False)
            )
            (output): BertSelfOutput(
              (dense): Linear(in_features=768, out_features=768, bias=True)
              (LayerNorm): BertLayerNorm()
              (dropout): Dropout(p=0.1, inplace=False)
            )
          )
          (intermediate): BertIntermediate(
            (dense): Linear(in_features=768, out_features=3072, bias=True)
          )
          (output): BertOutput(
            (dense): Linear(in_features=3072, out_features=768, bias=True)
            (LayerNorm): BertLayerNorm()
            (dropout): Dropout(p=0.1, inplace=False)
          )
        )
      )
    )
    (pooler): BertPooler(
      (dense): Linear(in_features=768, out_features=768, bias=True)
      (activation): Tanh()
    )
  )
  (fc): Linear(in_features=768, out_features=11, bias=True)
)>
Epoch [1/50]
Traceback (most recent call last):
  File "/home/<USER>/JHLiu/ChiFraud/run.py", line 55, in <module>
    train(config, model, train_iter, dev_iter, test_iter)
  File "/home/<USER>/JHLiu/ChiFraud/train_eval.py", line 41, in train
    outputs = model(trains)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1773, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1784, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/JHLiu/ChiFraud/models/Bert.py", line 46, in forward
    _, pooled = self.bert(context, attention_mask=mask, output_all_encoded_layers=False)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1773, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1784, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/JHLiu/ChiFraud/pytorch_pretrained/modeling.py", line 731, in forward
    encoded_layers = self.encoder(embedding_output,
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1773, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1784, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/JHLiu/ChiFraud/pytorch_pretrained/modeling.py", line 406, in forward
    hidden_states = layer_module(hidden_states, attention_mask)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1773, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1784, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/JHLiu/ChiFraud/pytorch_pretrained/modeling.py", line 391, in forward
    attention_output = self.attention(hidden_states, attention_mask)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1773, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1784, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/JHLiu/ChiFraud/pytorch_pretrained/modeling.py", line 349, in forward
    self_output = self.self(input_tensor, attention_mask)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1773, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/fraud/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1784, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/JHLiu/ChiFraud/pytorch_pretrained/modeling.py", line 321, in forward
    context_layer = torch.matmul(attention_probs, value_layer)
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 96.00 MiB. GPU 0 has a total capacity of 31.37 GiB of which 23.50 MiB is free. Including non-PyTorch memory, this process has 31.34 GiB memory in use. Of the allocated memory 30.63 GiB is allocated by PyTorch, and 118.12 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
========================================
运行完成时间: Thu Aug 21 14:26:55 UTC 2025
