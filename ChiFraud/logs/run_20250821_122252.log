========================================
运行时间: Thu Aug 21 12:22:52 UTC 2025
========================================
Model name 'bert-base-chinese' was not found in model name list (bert-base-uncased, bert-large-uncased, bert-base-cased, bert-large-cased, bert-base-multilingual-uncased, bert-base-multilingual-cased, bert-base-chinese). We assumed 'https://s3.amazonaws.com/models.huggingface.co/bert/bert-base-chinese-vocab.txt' was a path or url but couldn't find any file associated to this path or url.
Loading data...

0it [00:00, ?it/s]
1it [00:00, 16008.79it/s]
Traceback (most recent call last):
  File "/home/<USER>/JHLiu/ChiFraud/run.py", line 41, in <module>
    vocab, train_data, dev_data, test_data = build_dataset(config, args.word)
  File "/home/<USER>/JHLiu/ChiFraud/utils_bert.py", line 39, in build_dataset
    train = load_dataset(config.train_path, config.pad_size)
  File "/home/<USER>/JHLiu/ChiFraud/utils_bert.py", line 23, in load_dataset
    token = config.tokenizer.tokenize(content)
AttributeError: 'NoneType' object has no attribute 'tokenize'
========================================
运行完成时间: Thu Aug 21 12:57:50 UTC 2025
