#!/bin/bash
# model: <PERSON><PERSON>N<PERSON>, <PERSON>T<PERSON>t, Transformer, <PERSON>, Chinese_Bert

# 创建日志目录
mkdir -p logs

# 生成带时间戳的日志文件名
LOG_FILE="logs/run_$(date '+%Y%m%d_%H%M%S').log"

echo "开始运行训练，日志将保存到: $LOG_FILE"
echo "========================================" | tee "$LOG_FILE"
echo "运行时间: $(date)" | tee -a "$LOG_FILE"
echo "========================================" | tee -a "$LOG_FILE"


# 运行python脚本并同时输出到终端和日志文件
python run.py \
   --model Bert \
   --embedding random \
   --mode train \
   2>&1 | tee -a "$LOG_FILE"

# 在日志结尾添加完成时间
echo "========================================" | tee -a "$LOG_FILE"
echo "运行完成时间: $(date)" | tee -a "$LOG_FILE"
echo "日志已保存到: $LOG_FILE"
