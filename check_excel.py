#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys

try:
    # 读取Excel文件
    df = pd.read_excel('simplified_motivations_mapping.xlsx')
    print('Excel文件读取成功！')
    print(f'数据形状: {df.shape}')
    print(f'列名: {list(df.columns)}')
    print('\n前5行数据:')
    print(df.head())
    print('\n数据类型:')
    print(df.dtypes)
    
    # 检查是否有非空数据
    print('\n非空值统计:')
    print(df.count())
    
except Exception as e:
    print(f'读取Excel文件时出错: {e}')
    sys.exit(1)
