"""
Single-Agent Inference Module for CoMaPOI

This script performs inference for POI prediction using a single agent approach.
It uses either a local vLLM API or OpenAI API to generate predictions.
"""
import os
import json
import time
import argparse
import random
import numpy as np
import torch
from tqdm import tqdm
from concurrent.futures import Process<PERSON>oolExecutor, as_completed
from openai import OpenAI
from utils import *
from parser_tool import *
from evaluate import evaluate_poi_predictions


def set_random_seed(seed):
    """
    Set random seed for reproducibility across all random number generators.

    Args:
        seed: Integer seed value
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)


class OpenAIClient:
    """
    Client for making API calls to either OpenAI API or local vLLM deployment.
    """

    @staticmethod
    def create_client(args):
        """
        Create an OpenAI client configured for either OpenAI API or local vLLM.

        Args:
            args: Command line arguments

        Returns:
            OpenAI: Configured client
        """
        if args.openai:  # If using OpenAI's GPT models
            openai_api_base = "https://api.openai.com/v1"  # OpenAI official API address
            openai_api_key = os.getenv("OPENAI_API_KEY")  # Get OpenAI API key from environment
            client = OpenAI(
                api_key=openai_api_key,
                base_url=openai_api_base,
            )
        else:  # Using local vLLM deployment
            openai_api_base = f"http://localhost:{args.port}/v1"  # Local vLLM deployment address
            openai_api_key = "EMPTY"  # Local service doesn't need an actual API key
            client = OpenAI(
                api_key=openai_api_key,
                base_url=openai_api_base,
            )

        return client


class POIPredictor:
    """
    Class for predicting POIs using a language model.
    """

    @staticmethod
    def predict_single_sample(params):
        """
        Predict POIs for a single sample with retry mechanism.

        Args:
            params: Tuple containing (prompt, user_id, label, args)

        Returns:
            tuple: (user_id, prompt, label, generated_text)
        """
        prompt, user_id, label, args = params

        # Create OpenAI client
        client = OpenAIClient.create_client(args)

        # Define retry mechanism
        retry_count = 5
        retry_interval = 1

        print(f"Processing user {user_id}")

        for _ in range(retry_count):
            try:
                response = client.chat.completions.create(
                    model=args.model,
                    messages=[
                        {"role": "system", "content": "You are a helpful assistant."},
                        {"role": "user", "content": prompt},
                    ],
                    temperature=0,  # Fixed at 0 to eliminate randomness
                    top_p=1,        # Sample all possibilities
                    n=1,            # Ensure only one result is generated each time
                    max_tokens=args.max_tokens,
                )

                # Extract the response
                msg = response.choices[0].message.content.strip()
                return user_id, prompt, label, msg
            except Exception as e:
                print(f"Request failed for user {user_id}, retrying: {e}")
                time.sleep(retry_interval)
                retry_interval *= 2  # Exponential backoff

        return user_id, prompt, label, "API request failed"

    @staticmethod
    def process_prediction(generated_text, args):
        """
        Process the generated text to extract and clean predicted POIs.

        Args:
            generated_text: Text generated by the model
            args: Command line arguments

        Returns:
            list: List of valid POI IDs
        """
        predicted_pois = extract_predicted_pois(generated_text, args.top_k)
        if not predicted_pois:
            print(f"predicted_pois is empty, original output: {generated_text}\n")

        valid_poi_ids = clean_predicted_pois(predicted_pois, args.max_item)
        return valid_poi_ids


class InferenceProcessor:
    """
    Main class for processing inference tasks.
    """

    def __init__(self, args):
        """
        Initialize the inference processor with command line arguments.

        Args:
            args: Command line arguments
        """
        self.args = args

    def run_inference(self):
        """
        Run the inference process using multiple workers.

        Returns:
            str: Path to the output JSON file
        """
        args = self.args
        n, dataset, top_k = args.num_samples, args.dataset, args.top_k

        # Set up file paths
        if args.op_str == 'none':
            results_path = f'results/{dataset}/{args.save_name}'
        else:
            results_path = f'results/{args.op_str}/{dataset}/{args.save_name}'
        data_path = f'dataset_all/{dataset}/{args.mode}'

        os.makedirs(results_path, exist_ok=True)
        os.makedirs(data_path, exist_ok=True)

        output_json = f'{results_path}/poi_predictions.json'
        metrics_txt = f'{results_path}/metrics.txt'
        metrics_csv = f'{results_path}/metrics.csv'

        # Load dataset
        samples = []
        with open(args.data_path, 'r') as f:
            for line in f:
                samples.append(json.loads(line))

        num_samples = min(n, len(samples))
        samples = samples[:num_samples]

        # Prepare parameter list
        params_list = []
        print(f"Starting from sample: {args.start_point}")
        for i in range(args.start_point, n):
            selected_sample = samples[i % num_samples]
            if args.prompt_format == 'json':
                user_id, prompt, label = create_prompt_json(args, selected_sample)
            else:
                user_id, prompt, label = create_prompt_ori(args, selected_sample)

            if i == 0:
                print(f"Sample user_id: {user_id}, prompt: {prompt[:100]}..., label: {label}\n")

            params_list.append((prompt, user_id, label, args))

        # Create multiprocessing pool and process concurrently
        all_predictions = {}
        with ProcessPoolExecutor(max_workers=args.batch_size) as executor:
            futures = [executor.submit(POIPredictor.predict_single_sample, params) for params in params_list]

            # Wait for tasks to complete asynchronously
            for future in tqdm(as_completed(futures), total=len(futures), desc="Processing samples", colour="green"):
                user_id, prompt, label, generated_text = future.result()
                valid_poi_ids = POIPredictor.process_prediction(generated_text, args)
                print(f"User[{user_id}] Prediction: {valid_poi_ids}\n")

                all_predictions[user_id] = {
                    "user_id": user_id,
                    "input": prompt,
                    "label": label,
                    "raw_response": generated_text,
                    "predicted_poi_ids": valid_poi_ids[:args.top_k]
                }

                # Save and evaluate at intervals
                if len(all_predictions) % args.test_interval == 0:
                    print(f"\n[INFO] Completed {len(all_predictions)} samples. Saving interim results and evaluating.")
                    interim_output_json = f'{results_path}/interim_poi_predictions_{len(all_predictions)}.json'

                    # Save interim prediction results to JSON file
                    with open(interim_output_json, 'w', encoding='utf-8') as f:
                        json.dump(list(all_predictions.values()), f, ensure_ascii=False, indent=4)

                    # Evaluate interim results
                    evaluate_poi_predictions(args, interim_output_json, args.top_k, metrics_txt, metrics_csv, key='predicted_poi_ids')
                    print(f"Interim results and metrics saved for {len(all_predictions)} samples.")

        # Save all prediction results
        with open(output_json, 'w', encoding='utf-8') as f:
            json.dump(list(all_predictions.values()), f, ensure_ascii=False, indent=4)
        print(f"All prediction results saved to: {output_json}")

        # Display final results
        print("All prediction results:")
        for user_id, prediction in all_predictions.items():
            print(f"User {user_id} prediction result: {prediction['predicted_poi_ids']}")

        # Evaluate performance
        evaluate_poi_predictions(args, output_json, args.top_k, metrics_txt, metrics_csv, key='predicted_poi_ids')
        print("Evaluation completed.")

        return output_json


def parse_arguments():
    """
    Parse command line arguments.

    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description="POI Prediction using OpenAI API with Concurrency")
    parser.add_argument('--num_samples', type=int, default=0, help='Number of samples (0 for all)')
    parser.add_argument('--dataset', type=str, default='nyc', choices=['nyc', 'tky', 'ca'], help='Dataset to use')
    parser.add_argument('--data_path', type=str, default='dataset_all', help='Path to dataset')
    parser.add_argument('--top_k', type=int, default=10, help='Top K predictions')
    parser.add_argument('--APM', type=str, default='', choices=['yes', 'none'], help='APM option')
    parser.add_argument('--MPP', type=str, default='', choices=['yes', 'none'], help='MPP option')
    parser.add_argument('--api_type', type=str, default="vllm", help='API type')
    parser.add_argument('--max_item', type=int, default=4091, help='Maximum POI ID value')
    parser.add_argument('--num_ctx', type=int, default=4096, help='Context length')
    parser.add_argument('--start_point', type=int, default=0, help='Starting point')
    parser.add_argument('--test_interval', type=int, default=500, help='Test interval for saving and evaluating')
    parser.add_argument('--mode', type=str, default='test', help='Test mode')
    parser.add_argument('--save_name', type=str, default='N1', help='Save ID')
    parser.add_argument('--store_save_name', action="store_true", help='Manually provide storage name')
    parser.add_argument('--batch_size', type=int, default=1, help='Number of concurrent processes')
    parser.add_argument('--model', type=str, default='qwen2.5:7b', help='Model to use')
    parser.add_argument('--prompt_format', type=str, default='json', help='Prompt format')
    parser.add_argument('--openai', action="store_true", help="Use OpenAI API instead of local vLLM")
    parser.add_argument('--alpaca', action="store_true", help="Use Alpaca format")
    parser.add_argument('--port', type=int, default=7862, help='Port for local vLLM API')
    parser.add_argument('--max_tokens', type=int, default=256, help='Maximum tokens for generation')
    parser.add_argument('--op_str', type=str, default='none', help='Operation string for output directory naming')

    return parser.parse_args()


def main():
    """
    Main function to run the inference process.
    """
    # Parse arguments
    args = parse_arguments()
    dataset = args.dataset

    # Set dataset-specific parameters
    args.max_item = {"nyc": 5091, "tky": 7851, "ca": 13630}.get(dataset, 5091)
    if args.num_samples == 0:
        args.num_samples = {"nyc": 989, "tky": 2206, "ca": 1818}.get(dataset, 989)

    # Set data path based on format
    if args.alpaca:
        args.data_path = f'dataset_all/{dataset}/{args.mode}/{dataset}_{args.mode}_alpaca.jsonl'
    else:
        args.data_path = f'dataset_all/{dataset}/{args.mode}/{dataset}_{args.mode}.jsonl'

    # Set save name if not manually provided
    if not args.store_save_name:
        args.save_name = f"[Ori_{args.dataset}_{args.model}_{args.batch_size}_{args.prompt_format}_max_tokens{args.max_tokens}]"

    # Print arguments
    print("Parameter list:")
    for arg, value in vars(args).items():
        print(f"{arg}: {value}")

    # Create processor and run inference
    processor = InferenceProcessor(args)
    processor.run_inference()


if __name__ == "__main__":
    set_random_seed(2024)  # Set random seed for reproducibility
    main()
