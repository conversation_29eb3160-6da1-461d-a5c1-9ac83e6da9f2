#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re

def remove_brackets(text):
    """
    去除文本中的括号和括号内容
    支持：() （） [] 【】 {} 《》 「」
    """
    if pd.isna(text) or not isinstance(text, str):
        return ""

    # 定义括号对，包括中文和英文括号
    bracket_patterns = [
        r'\([^)]*\)',      # 英文圆括号 ()
        r'（[^）]*）',      # 中文圆括号 （）
        r'\[[^\]]*\]',     # 方括号 []
        r'【[^】]*】',      # 中文方括号 【】
        r'\{[^}]*\}',      # 花括号 {}
        r'《[^》]*》',      # 书名号 《》
        r'「[^」]*」',      # 日式引号 「」
    ]

    result = text
    for pattern in bracket_patterns:
        result = re.sub(pattern, '', result)

    # 清理多余的空格
    result = re.sub(r'\s+', ' ', result).strip()
    return result

def main():
    # 读取Excel文件
    try:
        df = pd.read_excel('simplified_motivations_mapping.xlsx')
        print(f"成功读取Excel文件，共 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")
        
        # 显示前几行原始数据
        print("\n=== 原始数据示例 ===")
        for i in range(min(5, len(df))):
            if pd.notna(df.iloc[i]['new_label']):
                print(f"行 {i+1}: {df.iloc[i]['new_label']}")
        
        # 清理括号内容
        print("\n=== 开始清理括号内容 ===")
        
        # 处理original列
        if 'original' in df.columns:
            df['original_cleaned'] = df['original'].apply(remove_brackets)
            print("已清理original列的括号内容")
        
        # 处理new_label列
        if 'new_label' in df.columns:
            df['new_label_cleaned'] = df['new_label'].apply(remove_brackets)
            print("已清理new_label列的括号内容")
        
        # 显示清理后的数据
        print("\n=== 清理后数据示例 ===")
        for i in range(min(5, len(df))):
            if pd.notna(df.iloc[i]['new_label']):
                original = df.iloc[i]['new_label']
                cleaned = df.iloc[i]['new_label_cleaned']
                print(f"行 {i+1}:")
                print(f"  原始: {original}")
                print(f"  清理: {cleaned}")
                print()
        
        # 保存清理后的文件
        output_file = 'simplified_motivations_mapping_cleaned.xlsx'
        df.to_excel(output_file, index=False)
        print(f"清理完成，结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"处理失败: {str(e)}")

if __name__ == "__main__":
    main()
